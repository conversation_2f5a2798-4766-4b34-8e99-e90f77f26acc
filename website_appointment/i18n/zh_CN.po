# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_appointment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_top
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>未发布"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_validated
msgid "<i class=\"fa fa-long-arrow-left me-2\"/>All Appointments"
msgstr "<i class=\"fa fa-long-arrow-left me-2\"/>所有预约"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_body
msgid ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                    <span class=\"o_not_editable\">Online</span>"
msgstr ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                    <span class=\"o_not_editable\">网上进行</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_pages_kanban_view
msgid "<span class=\"fa fa-globe\" title=\"website\"/>"
msgstr "<span class=\"fa fa-globe\" title=\"website\"/>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Bistr-Odoo</span>"
msgstr "<span itemprop=\"name\">Odoo酒吧</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Doctor's Office</span>"
msgstr "<span itemprop=\"name\">医生办公室</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Online</span>"
msgstr "<span itemprop=\"name\">在线</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Tennis Club</span>"
msgstr "<span itemprop=\"name\">网球俱乐部</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "<span>See all availabilities <i class=\"fa fa-long-arrow-right\"/></span>"
msgstr "<span>查看所有可用性<i class=\"fa fa-long-arrow-right\"/></span>"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
msgid "A first step in joining our team as a technical consultant."
msgstr "加入我们团队成为技术顾问的第一步。"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a function here..."
msgstr "此处添加功能..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a resource description here..."
msgstr "在此处添加资源描述…"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a user description here..."
msgstr "在此处添加用户描述……"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_body
msgid "Add an intro message here..."
msgstr "在此处添加一条介绍消息..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All Types"
msgstr "所有类型"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All assigned users"
msgstr "所有已指派用户"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Allow Guests"
msgstr "允许访客"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
#: model:website.menu,name:website_appointment.menu_appointment
msgid "Appointment"
msgstr "约会"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_online_appointment_snippet
msgid "Appointment Button"
msgstr "预约按钮"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_invite
msgid "Appointment Invite"
msgstr "预约邀请"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.action_appointment_pages_list
msgid "Appointment Pages"
msgstr "预约网页"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_type
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointment Type"
msgstr "约会类型"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Appointment Type Name"
msgstr "预约类型名称"

#. module: website_appointment
#: model:ir.ui.menu,name:website_appointment.menu_appointment_pages
#: model:ir.ui.menu,name:website_appointment.website_appointment_type_menu
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
#: model_terms:ir.ui.view,arch_db:website_appointment.snippets
msgid "Appointments"
msgstr "预约"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointments Page"
msgstr "预约网页"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment
msgid "Book an Appointment"
msgstr "预约一次会面"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__can_publish
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__can_publish
msgid "Can Publish"
msgstr "可以发布"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
msgid "Candidate Interview"
msgstr "候选人面试"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Card Design"
msgstr "卡片设计"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Choose who you will meet"
msgstr "选择您要见的人"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Choose your appointment"
msgstr "选择您的预约"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Comma-separated list of parts of appointment names"
msgstr "以逗号分隔的任命名称部分列表"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Contact us"
msgstr "联系我们"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Dental Care"
msgstr "牙科护理"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__appointment_type_warning_msg
msgid "Different Website Message"
msgstr "不同的网站信息"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/js/appointment_frontend/appointment_form.js:0
msgid "Error"
msgstr "错误"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Filter On"
msgstr "筛选打开"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Go back to Appointment"
msgstr "返回预约"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__is_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_published
msgid "Is Published"
msgstr "已发布"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Meeting"
msgstr "会议"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Names"
msgstr "名称"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.appointment_type_action_add_simplified
msgid "New Appointment Type"
msgstr "新预约类型"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "No Filter"
msgstr "无筛选器"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "No result for \""
msgstr "没有\"...... \"的结果"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Online Cooking Lesson"
msgstr "在线烹饪课程"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Operator"
msgstr "操作员"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Please fill this field"
msgstr "请填写此字段"

#. module: website_appointment
#: model:website.snippet.filter,name:website_appointment.website_snippet_filter_appointments
msgid "Public Appointments"
msgstr "公开预约"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Published"
msgstr "已发布"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Resources"
msgstr "资源"

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_id
msgid "Restrict to a specific website."
msgstr "仅限于特定网站。"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO优化"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.dynamic_filter_template_appointment_type_card
msgid "Sample"
msgstr "示例"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
msgid "Schedule an appointment"
msgstr "预约"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Search..."
msgstr "搜索..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "See all appointments <i class=\"fa fa-long-arrow-right\"/>"
msgstr "查看所有预约<i class=\"fa fa-long-arrow-right\"/>"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__seo_name
msgid "Seo name"
msgstr "Seo 名称"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show Duration"
msgstr "显示时长"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show Time Zone"
msgstr "显示时区"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show User/Resource Pictures"
msgstr "显示用户/资源图片"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Specific Resources"
msgstr "特定资源"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific Types"
msgstr "特定类型"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Specific Users"
msgstr "特定用户"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific users"
msgstr "特定用户"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/controllers/appointment.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptcha 检测到可疑活动。"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Table"
msgstr "餐桌"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Tennis Court"
msgstr "网球场"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/appointment_invite.py:0
msgid ""
"The following appointment type(s) are not compatible with the website "
"chosen: %(appointments)s"
msgstr "以下预约类型与所选网站不兼容：%(appointments)s"

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_url
msgid "The full URL to access the document through the website."
msgstr "通过网站访问文档的完整网址。"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "There is currently no appointment available"
msgstr "目前没有预约"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_list_layout
msgid "Use the top button '<b>+ New</b>' to create an appointment type."
msgstr "使用顶部按钮“<b>+新的</b>”创建约会类型。"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Users"
msgstr "用户"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_published
msgid "Visible on current website"
msgstr "在当前网站显示"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_id
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Website"
msgstr "网站"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "网站片段筛选"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_url
msgid "Website URL"
msgstr "网站网址"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_description
msgid "Website meta description"
msgstr "网站原说明"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_keywords
msgid "Website meta keywords"
msgstr "网站meta关键词"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_title
msgid "Website meta title"
msgstr "网站标题meta元素"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_og_img
msgid "Website opengraph image"
msgstr "网站opengraph图像"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "contact us"
msgstr "联系我们"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "e.g. \"Technical Demo\""
msgstr "例如：\"技术演示\""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "e.g. Dental Care, ..."
msgstr "例如：牙科保健⋯"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "or"
msgstr "或"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "⌙ Resources"
msgstr "⌙ 资源"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Specify"
msgstr "⌙ 指定"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Users"
msgstr "⌙ 用户"
