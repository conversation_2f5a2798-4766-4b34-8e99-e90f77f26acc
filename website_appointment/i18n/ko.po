# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_appointment
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil <PERSON>do<PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_top
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>게시 안 됨"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_validated
msgid "<i class=\"fa fa-long-arrow-left me-2\"/>All Appointments"
msgstr "<i class=\"fa fa-long-arrow-left me-2\"/>모든 일정"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_body
msgid ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                    <span class=\"o_not_editable\">Online</span>"
msgstr ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                    <span class=\"o_not_editable\">온라인</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_pages_kanban_view
msgid "<span class=\"fa fa-globe\" title=\"website\"/>"
msgstr "<span class=\"fa fa-globe\" title=\"웹사이트\"/>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Bistr-Odoo</span>"
msgstr "<span itemprop=\"name\">Bistr-Odoo</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Doctor's Office</span>"
msgstr "<span itemprop=\"name\">병원</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Online</span>"
msgstr "온라인"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Tennis Club</span>"
msgstr "<span itemprop=\"name\">테니스 클럽</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "<span>See all availabilities <i class=\"fa fa-long-arrow-right\"/></span>"
msgstr "<span>모든 가능한 일정 보기 <i class=\"fa fa-long-arrow-right\"/></span>"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
msgid "A first step in joining our team as a technical consultant."
msgstr "기술 컨설턴트로서 팀에 합류하기 위한 첫 단계입니다."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a function here..."
msgstr "여기에 기능을 추가하세요..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a resource description here..."
msgstr "여기에 리소스 설명을 추가하세요..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a user description here..."
msgstr "여기에 사용자 설명을 추가하세요..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_body
msgid "Add an intro message here..."
msgstr "여기에 소개 메시지를 추가하세요..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All Types"
msgstr "모든 유형"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All assigned users"
msgstr "지정된 모든 사용자"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Allow Guests"
msgstr "게스트 허용"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
#: model:website.menu,name:website_appointment.menu_appointment
msgid "Appointment"
msgstr "일정"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_online_appointment_snippet
msgid "Appointment Button"
msgstr "예약 버튼"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_invite
msgid "Appointment Invite"
msgstr "약속 ㅊㅗ대"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.action_appointment_pages_list
msgid "Appointment Pages"
msgstr "일정 페이지"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_type
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointment Type"
msgstr "약속 유형"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Appointment Type Name"
msgstr "일정 유형 이름"

#. module: website_appointment
#: model:ir.ui.menu,name:website_appointment.menu_appointment_pages
#: model:ir.ui.menu,name:website_appointment.website_appointment_type_menu
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
#: model_terms:ir.ui.view,arch_db:website_appointment.snippets
msgid "Appointments"
msgstr "일정"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointments Page"
msgstr "일정 페이지"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment
msgid "Book an Appointment"
msgstr "일정 예약하기"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__can_publish
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__can_publish
msgid "Can Publish"
msgstr "게시 가능"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
msgid "Candidate Interview"
msgstr "지원자 면접"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Card Design"
msgstr "카드 디자인"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Choose who you will meet"
msgstr "만날 사람을 선택하세요"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Choose your appointment"
msgstr "약속을 선택하세요."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Comma-separated list of parts of appointment names"
msgstr "쉼표로 구분된 일정 이름 부분 목록"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Contact us"
msgstr "문의하기"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Dental Care"
msgstr "치과 치료"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__appointment_type_warning_msg
msgid "Different Website Message"
msgstr "다른 웹사이트 메시지"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/js/appointment_frontend/appointment_form.js:0
msgid "Error"
msgstr "오류"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Filter On"
msgstr "필터 적용"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Go back to Appointment"
msgstr "일정으로 돌아가기"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__is_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_published
msgid "Is Published"
msgstr "게시 여부"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Meeting"
msgstr "회의"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Names"
msgstr "이름"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.appointment_type_action_add_simplified
msgid "New Appointment Type"
msgstr "새로운 약속 유형"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "No Filter"
msgstr "필터 없음"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "No result for \""
msgstr "\" 에 대한 결과가 없습니다."

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Online Cooking Lesson"
msgstr "온라인 쿠킹 레슨"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Operator"
msgstr "운영자"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Please fill this field"
msgstr "이 필드를 입력해 주세요."

#. module: website_appointment
#: model:website.snippet.filter,name:website_appointment.website_snippet_filter_appointments
msgid "Public Appointments"
msgstr "공개 예약"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Published"
msgstr "게시 완료"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Resources"
msgstr "자원"

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_id
msgid "Restrict to a specific website."
msgstr "특정 웹사이트로만 제한."

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO 최적화"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.dynamic_filter_template_appointment_type_card
msgid "Sample"
msgstr "견본"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
msgid "Schedule an appointment"
msgstr "미팅 예약하기"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Search..."
msgstr "검색..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "See all appointments <i class=\"fa fa-long-arrow-right\"/>"
msgstr "모든 예약 보기 <i class=\"fa fa-long-arrow-right\"/>"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__seo_name
msgid "Seo name"
msgstr "Seo 이름"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show Duration"
msgstr "기간 표시"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show Time Zone"
msgstr "시간대 보기"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show User/Resource Pictures"
msgstr "사용자/리소스 사진 보기"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Specific Resources"
msgstr "특정 리소스"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific Types"
msgstr "특정 유형"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Specific Users"
msgstr "특정 사용자"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific users"
msgstr "특정 사용자"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/controllers/appointment.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "구글 reCaptcha에서 의심스러운 활동이 감지되었습니다."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Table"
msgstr "테이블"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Tennis Court"
msgstr "테니스 코트"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/appointment_invite.py:0
msgid ""
"The following appointment type(s) are not compatible with the website "
"chosen: %(appointments)s"
msgstr "다음 예약 유형은 선택한 웹사이트와 호환되지 않습니다: %(appointments)s"

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_url
msgid "The full URL to access the document through the website."
msgstr "웹사이트를 통해 문서에 접근 하는 전체 URL입니다."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "There is currently no appointment available"
msgstr "현재 예약 가능한 일정이 없습니다."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_list_layout
msgid "Use the top button '<b>+ New</b>' to create an appointment type."
msgstr "새로운 일정 카테고리를 생성하려면 위쪽의 '<b>+ New</b>' 버튼을 클릭해주세요."

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Users"
msgstr "사용자"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_published
msgid "Visible on current website"
msgstr "현재 웹 사이트에 공개"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_id
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Website"
msgstr "웹사이트"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "웹사이트 스니펫 필터"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_description
msgid "Website meta description"
msgstr "웹사이트 메타 설명"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_keywords
msgid "Website meta keywords"
msgstr "웹사이트 메타 핵심어"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_title
msgid "Website meta title"
msgstr "웹사이트 메타 제목"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_og_img
msgid "Website opengraph image"
msgstr "웹사이트 오픈그래프 이미지"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "contact us"
msgstr "문의하기"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "e.g. \"Technical Demo\""
msgstr "예: \"기술 관련 데모\""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "e.g. Dental Care, ..."
msgstr "예: 치과 진료, ..."

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "or"
msgstr "또는"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "⌙ Resources"
msgstr "⌙ 리소스"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Specify"
msgstr "⌙ 지정"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Users"
msgstr "⌙ 사용자"
