# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_appointment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2025
# <PERSON>, 2025
# Naser mars, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Naser mars, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_top
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>منتشر نشده"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_validated
msgid "<i class=\"fa fa-long-arrow-left me-2\"/>All Appointments"
msgstr "<i class=\"fa fa-long-arrow-left me-2\"/>تمام ملاقات‌ها"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_body
msgid ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                    <span class=\"o_not_editable\">Online</span>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_pages_kanban_view
msgid "<span class=\"fa fa-globe\" title=\"website\"/>"
msgstr "<span class=\"fa fa-globe\" title=\"website\"/>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Bistr-Odoo</span>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Doctor's Office</span>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Online</span>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Tennis Club</span>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "<span>See all availabilities <i class=\"fa fa-long-arrow-right\"/></span>"
msgstr ""
"<span>مشاهده تمام زمان‌های در دسترس<i class=\"fa fa-long-arrow-"
"right\"/></span>"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
msgid "A first step in joining our team as a technical consultant."
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a function here..."
msgstr "کارکردی را در اینجا اضافه کنید ..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a resource description here..."
msgstr "شرح منبع را در اینجا اضافه کنید..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a user description here..."
msgstr "توضیحات کاربر را در اینجا اضافه کنید..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_body
msgid "Add an intro message here..."
msgstr "یک پیام مقدماتی در اینجا اضافه کنید..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All Types"
msgstr "همه انواع"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All assigned users"
msgstr "همه کاربران اختصاص داده شده"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Allow Guests"
msgstr "اجازه دادن به کاربر مهمان"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
#: model:website.menu,name:website_appointment.menu_appointment
msgid "Appointment"
msgstr "قرار ملاقات"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_online_appointment_snippet
msgid "Appointment Button"
msgstr "دکمه قرار ملاقات"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_invite
msgid "Appointment Invite"
msgstr "دعوت برای قرار ملاقات"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.action_appointment_pages_list
msgid "Appointment Pages"
msgstr "صفحات قرار ملاقات"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_type
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointment Type"
msgstr "نوع قرار"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Appointment Type Name"
msgstr "نام نوع قرار"

#. module: website_appointment
#: model:ir.ui.menu,name:website_appointment.menu_appointment_pages
#: model:ir.ui.menu,name:website_appointment.website_appointment_type_menu
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
#: model_terms:ir.ui.view,arch_db:website_appointment.snippets
msgid "Appointments"
msgstr "قرار ملاقات ها"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointments Page"
msgstr "صفحه قرار ملاقات ها"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment
msgid "Book an Appointment"
msgstr "یک قرار ملاقات رزرو کنید"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__can_publish
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__can_publish
msgid "Can Publish"
msgstr "می تواند منتشر کند"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
msgid "Candidate Interview"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Card Design"
msgstr "طراحی کارت"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Choose who you will meet"
msgstr "انتخاب کنید با چه کسی ملاقات خواهید کرد"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Choose your appointment"
msgstr "قرار ملاقات موردنظر خود را انتخاب کنید"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Comma-separated list of parts of appointment names"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Contact us"
msgstr "تماس با ما"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Dental Care"
msgstr "خدمات دندان‌پزشکی"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__appointment_type_warning_msg
msgid "Different Website Message"
msgstr "پیام وب سایت متفاوت"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/js/appointment_frontend/appointment_form.js:0
msgid "Error"
msgstr "خطا"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Filter On"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Go back to Appointment"
msgstr "به انتصاب برگردید"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__is_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_published
msgid "Is Published"
msgstr "منتشر شده است"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Meeting"
msgstr "ملاقات"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Names"
msgstr ""

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.appointment_type_action_add_simplified
msgid "New Appointment Type"
msgstr "نوع قرار جدید"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "No Filter"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "No result for \""
msgstr "نتیجه ای برای \""

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Online Cooking Lesson"
msgstr "تدریس آنلاین آشپزی"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Operator"
msgstr "اپراتور"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Please fill this field"
msgstr "لطفا این فیلد را پر کنید"

#. module: website_appointment
#: model:website.snippet.filter,name:website_appointment.website_snippet_filter_appointments
msgid "Public Appointments"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Published"
msgstr "منتشر شده"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Resources"
msgstr "منابع"

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_id
msgid "Restrict to a specific website."
msgstr "محدود به یک وب سایت خاص."

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_seo_optimized
msgid "SEO optimized"
msgstr "سئو بهینه شده است"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.dynamic_filter_template_appointment_type_card
msgid "Sample"
msgstr "نمونه"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
msgid "Schedule an appointment"
msgstr "قرار ملاقات تعیین کنید"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Search..."
msgstr "جستجو..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "See all appointments <i class=\"fa fa-long-arrow-right\"/>"
msgstr "مشاهده تمام قرارها<i class=\"fa fa-long-arrow-right\"/>"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__seo_name
msgid "Seo name"
msgstr "نام سئو"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show Duration"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show Time Zone"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show User/Resource Pictures"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Specific Resources"
msgstr "منابع خاص"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific Types"
msgstr "انواع خاص"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Specific Users"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific users"
msgstr "کاربران خاص"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/controllers/appointment.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "فعالیت مشکوکی توسط گوگل reCaptcha شناسایی شد."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Table"
msgstr "میز"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Tennis Court"
msgstr "زمین تنیس"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/appointment_invite.py:0
msgid ""
"The following appointment type(s) are not compatible with the website "
"chosen: %(appointments)s"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_url
msgid "The full URL to access the document through the website."
msgstr "آدرس URL کامل برای دسترسی سند در وبسایت."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "There is currently no appointment available"
msgstr "در حال حاضر هیچ قرار ملاقاتی امکان‌پذیر نیست"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_list_layout
msgid "Use the top button '<b>+ New</b>' to create an appointment type."
msgstr "از دکمه بالایی استفاده کنید'<b>+جدید</b>'برای ایجاد نوع قرار ملاقات"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Users"
msgstr "کاربران"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_published
msgid "Visible on current website"
msgstr "قابل دید در وبسایت حاضر"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_id
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Website"
msgstr "تارنما"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "فیلتر قطعه وب سایت"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_url
msgid "Website URL"
msgstr "URL وبسایت"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_description
msgid "Website meta description"
msgstr "توضیحات متا وبسایت"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_keywords
msgid "Website meta keywords"
msgstr "کلیدواژه‌های متا وبسایت"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_title
msgid "Website meta title"
msgstr "عنوان متای وب سایت"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_og_img
msgid "Website opengraph image"
msgstr "تصویر opengraph وب سایت"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "contact us"
msgstr "با ما تماس بگیرید"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "e.g. \"Technical Demo\""
msgstr "به طورمثال «دموی فنی»"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "e.g. Dental Care, ..."
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "or"
msgstr "یا"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "⌙ Resources"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Specify"
msgstr "⌙ تعیین کنید"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Users"
msgstr "⌙کاربران"
