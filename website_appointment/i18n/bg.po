# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_appointment
# 
# Translators:
# <PERSON>, 2024
# Ива<PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_top
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>Непубликувано"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_validated
msgid "<i class=\"fa fa-long-arrow-left me-2\"/>All Appointments"
msgstr "<i class=\"fa fa-long-arrow-left me-2\"/>Всички уговорки"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_body
msgid ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                    <span class=\"o_not_editable\">Online</span>"
msgstr ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                    <span class=\"o_not_editable\">Онлайн</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_pages_kanban_view
msgid "<span class=\"fa fa-globe\" title=\"website\"/>"
msgstr "<span class=\"fa fa-globe\" title=\"website\"/>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Bistr-Odoo</span>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Doctor's Office</span>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Online</span>"
msgstr "<span itemprop=\"name\">Онлайн</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Tennis Club</span>"
msgstr "<span itemprop=\"name\">Тенис клуб</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "<span>See all availabilities <i class=\"fa fa-long-arrow-right\"/></span>"
msgstr ""

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
msgid "A first step in joining our team as a technical consultant."
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a function here..."
msgstr "Добавете функция тук..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a resource description here..."
msgstr "Добавете описание на ресурса тук..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a user description here..."
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_body
msgid "Add an intro message here..."
msgstr "Добавете въвеждащо съобщение тук..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All Types"
msgstr "Всички типове"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All assigned users"
msgstr "Всички назначени потребители"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Allow Guests"
msgstr "Разрешаване на гости"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
#: model:website.menu,name:website_appointment.menu_appointment
msgid "Appointment"
msgstr "Среща"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_online_appointment_snippet
msgid "Appointment Button"
msgstr ""

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_invite
msgid "Appointment Invite"
msgstr "Покана за среща"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.action_appointment_pages_list
msgid "Appointment Pages"
msgstr ""

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_type
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointment Type"
msgstr "Тип среща"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Appointment Type Name"
msgstr ""

#. module: website_appointment
#: model:ir.ui.menu,name:website_appointment.menu_appointment_pages
#: model:ir.ui.menu,name:website_appointment.website_appointment_type_menu
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
#: model_terms:ir.ui.view,arch_db:website_appointment.snippets
msgid "Appointments"
msgstr "Срещи"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointments Page"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment
msgid "Book an Appointment"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__can_publish
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__can_publish
msgid "Can Publish"
msgstr "Може да публикува"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
msgid "Candidate Interview"
msgstr "Интервю на кандидат"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Card Design"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Choose who you will meet"
msgstr "Изберете с кого ще се срещнете "

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Choose your appointment"
msgstr "Изберете вашата среща"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Comma-separated list of parts of appointment names"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Contact us"
msgstr "Свържете се с нас"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Dental Care"
msgstr "Грижа за зъбите"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__appointment_type_warning_msg
msgid "Different Website Message"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/js/appointment_frontend/appointment_form.js:0
msgid "Error"
msgstr "Грешка"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Filter On"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Go back to Appointment"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__is_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_published
msgid "Is Published"
msgstr "Е публикувано"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Meeting"
msgstr "Среща"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Names"
msgstr "Имена"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.appointment_type_action_add_simplified
msgid "New Appointment Type"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "No Filter"
msgstr "Без филтър"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "No result for \""
msgstr ""

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Online Cooking Lesson"
msgstr "Онлайн урок по готварство"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Operator"
msgstr "Оператор"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Please fill this field"
msgstr "Моля, попълнете това поле"

#. module: website_appointment
#: model:website.snippet.filter,name:website_appointment.website_snippet_filter_appointments
msgid "Public Appointments"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Published"
msgstr "Публикуван"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Resources"
msgstr "Ресурси"

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_id
msgid "Restrict to a specific website."
msgstr "Ограничете до конкретен уебсайт."

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO оптимизиран"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.dynamic_filter_template_appointment_type_card
msgid "Sample"
msgstr "Мостра"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
msgid "Schedule an appointment"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Search..."
msgstr "Потърсете..."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "See all appointments <i class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__seo_name
msgid "Seo name"
msgstr "СЕО име"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show Duration"
msgstr "Показване на продължителност"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show Time Zone"
msgstr "Показване на часова зона"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show User/Resource Pictures"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Specific Resources"
msgstr "Специфични ресурси"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific Types"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Specific Users"
msgstr "Специфични потребители"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific users"
msgstr "Специфични потребители"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/controllers/appointment.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Открита е подозрителна активност от Google reCaptcha"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Table"
msgstr "Таблица"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Tennis Court"
msgstr "Тенис корт"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/appointment_invite.py:0
msgid ""
"The following appointment type(s) are not compatible with the website "
"chosen: %(appointments)s"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_url
msgid "The full URL to access the document through the website."
msgstr "Пълен URL адрес за достъп до документа през уебсайта."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "There is currently no appointment available"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_list_layout
msgid "Use the top button '<b>+ New</b>' to create an appointment type."
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Users"
msgstr "Потребители"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_published
msgid "Visible on current website"
msgstr "Видимо на текущия уебсайт"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_id
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Website"
msgstr "Уебсайт"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Филтър за фрагменти от уебсайт."

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_url
msgid "Website URL"
msgstr "URL адрес на уебсайт"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_description
msgid "Website meta description"
msgstr "Мета описание на уебсайт"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_keywords
msgid "Website meta keywords"
msgstr "Мета ключови думи на уебсайт"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_title
msgid "Website meta title"
msgstr "Мета заглавие на уебсайт"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_og_img
msgid "Website opengraph image"
msgstr "Уебсайт opengraph изображение"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "contact us"
msgstr "свържете се с нас"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "e.g. \"Technical Demo\""
msgstr "напр. \"Техническа демонстрация\""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "e.g. Dental Care, ..."
msgstr "напр. Дентална грижа, ..."

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "or"
msgstr "или"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "⌙ Resources"
msgstr "⌙ Ресурси"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Specify"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Users"
msgstr "⌙ Потребители"
