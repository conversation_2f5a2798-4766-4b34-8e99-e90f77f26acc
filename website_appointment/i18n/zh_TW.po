# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_appointment
# 
# Translators:
# <PERSON>, 2025
# Wil <PERSON>do<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_top
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/> 未發佈"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_validated
msgid "<i class=\"fa fa-long-arrow-left me-2\"/>All Appointments"
msgstr "<i class=\"fa fa-long-arrow-left me-2\"/> 所有預約會面類型"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_body
msgid ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                    <span class=\"o_not_editable\">Online</span>"
msgstr ""
"<i class=\"fa fa-video-camera fa-fw me-1 fs-5 text-muted\"/>\n"
"                    <span class=\"o_not_editable\">網上進行</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_pages_kanban_view
msgid "<span class=\"fa fa-globe\" title=\"website\"/>"
msgstr "<span class=\"fa fa-globe\" title=\"網站\"/>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Bistr-Odoo</span>"
msgstr "<span itemprop=\"name\">Odoo 小酒館</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Doctor's Office</span>"
msgstr "<span itemprop=\"name\">醫生辦公室</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Online</span>"
msgstr "<span itemprop=\"name\">網上進行</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "<span itemprop=\"name\">Tennis Club</span>"
msgstr "<span itemprop=\"name\">網球俱樂部</span>"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "<span>See all availabilities <i class=\"fa fa-long-arrow-right\"/></span>"
msgstr "<span>查看所有可用時段 <i class=\"fa fa-long-arrow-right\"/></span>"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
msgid "A first step in joining our team as a technical consultant."
msgstr "加入我們團隊成為技術顧問的第一步。"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a function here..."
msgstr "在此加入功能⋯"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a resource description here..."
msgstr "在此加入資源描述⋯"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a user description here..."
msgstr "在此加入用戶描述⋯"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_card_body
msgid "Add an intro message here..."
msgstr "加入簡介資料⋯"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All Types"
msgstr "所有類型"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All assigned users"
msgstr "所有已指派用戶"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Allow Guests"
msgstr "允許訪客"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
#: model:website.menu,name:website_appointment.menu_appointment
msgid "Appointment"
msgstr "預約"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_online_appointment_snippet
msgid "Appointment Button"
msgstr "預約按鈕"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_invite
msgid "Appointment Invite"
msgstr "預約邀請"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.action_appointment_pages_list
msgid "Appointment Pages"
msgstr "預約頁面"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_type
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointment Type"
msgstr "預約類型"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Appointment Type Name"
msgstr "預約類型名稱"

#. module: website_appointment
#: model:ir.ui.menu,name:website_appointment.menu_appointment_pages
#: model:ir.ui.menu,name:website_appointment.website_appointment_type_menu
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
#: model_terms:ir.ui.view,arch_db:website_appointment.snippets
msgid "Appointments"
msgstr "預約"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointments Page"
msgstr "預約頁面"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment
msgid "Book an Appointment"
msgstr "預約一次會面"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__can_publish
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__can_publish
msgid "Can Publish"
msgstr "可以發佈"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
msgid "Candidate Interview"
msgstr "應徵者面試"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Card Design"
msgstr "卡片設計"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Choose who you will meet"
msgstr "選取要會見的人"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Choose your appointment"
msgstr "選擇您的預約"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Comma-separated list of parts of appointment names"
msgstr "預約名稱部分的列表，以逗號分隔"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Contact us"
msgstr "聯絡我們"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Dental Care"
msgstr "牙科護理"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__appointment_type_warning_msg
msgid "Different Website Message"
msgstr "不同的網站訊息"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/js/appointment_frontend/appointment_form.js:0
msgid "Error"
msgstr "錯誤"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Filter On"
msgstr "篩選於"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Go back to Appointment"
msgstr "返回預約列表"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__is_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_published
msgid "Is Published"
msgstr "已發佈"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Meeting"
msgstr "會議"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Names"
msgstr "名稱"

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.appointment_type_action_add_simplified
msgid "New Appointment Type"
msgstr "新的預約類型"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "No Filter"
msgstr "沒有篩選器"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "No result for \""
msgstr "找不到結果："

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Online Cooking Lesson"
msgstr "網上烹飪課程"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Operator"
msgstr "操作員"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Please fill this field"
msgstr "請填寫此欄位"

#. module: website_appointment
#: model:website.snippet.filter,name:website_appointment.website_snippet_filter_appointments
msgid "Public Appointments"
msgstr "公開預約"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Published"
msgstr "已發佈"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Resources"
msgstr "資源"

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_id
msgid "Restrict to a specific website."
msgstr "只限特定網站使用。"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO 已最佳化"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.dynamic_filter_template_appointment_type_card
msgid "Sample"
msgstr "範例"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
msgid "Schedule an appointment"
msgstr "預約會面"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Search..."
msgstr "搜尋⋯"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "See all appointments <i class=\"fa fa-long-arrow-right\"/>"
msgstr "查看所有預約 <i class=\"fa fa-long-arrow-right\"/>"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__seo_name
msgid "Seo name"
msgstr "SEO 名稱"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show Duration"
msgstr "顯示時長"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show Time Zone"
msgstr "顯示時區"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Show User/Resource Pictures"
msgstr "顯示使用者/資源圖片"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Specific Resources"
msgstr "特定資源"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific Types"
msgstr "特定類型"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Specific Users"
msgstr "特定使用者"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific users"
msgstr "特定使用者"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/controllers/appointment.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptcha 檢測到的可疑活動。"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Table"
msgstr "餐桌"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website_snippet_filter.py:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_appointments_preview_data
msgid "Tennis Court"
msgstr "網球場"

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/appointment_invite.py:0
msgid ""
"The following appointment type(s) are not compatible with the website "
"chosen: %(appointments)s"
msgstr "以下預約類型不兼容所選網站：%(appointments)s"

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_url
msgid "The full URL to access the document through the website."
msgstr "通過網站存取此文件的完整網址."

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "There is currently no appointment available"
msgstr "目前沒有可用預約"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_list_layout
msgid "Use the top button '<b>+ New</b>' to create an appointment type."
msgstr "使用頂部按鈕 '<b>+ 建立</b>' 新增約會類型。"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "Users"
msgstr "使用者"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_published
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_published
msgid "Visible on current website"
msgstr "在當前網站可見"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_id
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_id
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Website"
msgstr "網站"

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "網站小摘要篩選器"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_invite__website_url
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_url
msgid "Website URL"
msgstr "網站網址"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_description
msgid "Website meta description"
msgstr "網站 meta 說明"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_keywords
msgid "Website meta keywords"
msgstr "網站meta關鍵字"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_title
msgid "Website meta title"
msgstr "網站meta標題"

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_og_img
msgid "Website opengraph image"
msgstr "網站 opengraph 圖片"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "contact us"
msgstr "聯絡我們"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "e.g. \"Technical Demo\""
msgstr "例：技術示範"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "e.g. Dental Care, ..."
msgstr "例：牙科護理，⋯"

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "or"
msgstr "或"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
msgid "⌙ Resources"
msgstr "⌙ 資源"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Specify"
msgstr "⌙ 指定"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Users"
msgstr "⌙ 使用者"
