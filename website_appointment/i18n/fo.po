# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_calendar
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:03+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Faroese (https://www.transifex.com/odoo/teams/41243/fo/)\n"
"Language: fo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_booking_resource_description
msgid "(+1 other)"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_pages_kanban_view
msgid "<i class=\"fa fa-globe me-2\" title=\"Website URL\"/>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "<i class=\"fa fa-long-arrow-left me-2\"/>All Appointments"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "<span class=\"navbar-brand h4 my-0 me-auto my-auto\">Choose Your Appointment</span>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_kanban
msgid "<span class=\"text-bg-success\">Published</span>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "<span> See all availabilities <i class=\"fa fa-long-arrow-right\"/></span>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a function here..."
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a resource description here..."
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Add a user description here..."
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "Add an intro message here..."
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_validated
msgid "All Appointments"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All Types"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "All assigned users"
msgstr ""

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
#: model:website.menu,name:website_appointment.menu_appointment
msgid "Appointment"
msgstr ""

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.action_appointment_pages_list
msgid "Appointment Pages"
msgstr ""

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_appointment_type
msgid "Appointment Type"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Appointment Type Name"
msgstr ""

#. module: website_appointment
#: model:ir.ui.menu,name:website_appointment.menu_appointment_pages
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Appointments"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Appointments Page"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment
msgid "Book an Appointment"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Booked<span class=\"chevron position-absolute d-block\"/>"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__can_publish
msgid "Can Publish"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.snippet_options
msgid "Card Design"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Choose"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Choose who you will meet"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Contact us"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__cover_properties
msgid "Cover Properties"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Details<span class=\"chevron position-absolute d-block\"/>"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "Go back to Appointment"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_published
msgid "Is Published"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Meeting"
msgstr ""

#. module: website_appointment
#: model:ir.actions.act_window,name:website_appointment.appointment_type_action_add_simplified
msgid "New Appointment Type"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "No appointment found."
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "Online"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_online_appointment_snippet
msgid "Online Appointment"
msgstr ""

#. module: website_appointment
#: model:ir.ui.menu,name:website_appointment.website_appointment_type_menu
msgid "Online Appointments"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Operator"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Please fill this field"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_form
msgid "Publish"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_kanban
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_search
msgid "Published"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_select_operator
msgid "Schedule"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_validated
msgid "Schedule Another Appointment"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "Schedule an Appointment"
msgstr ""

#. module: website_appointment
#. odoo-python
#: code:addons/website_appointment/models/website.py:0
msgid "Schedule an appointment"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.website_calendar_index_topbar
msgid "Search an appointment..."
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__seo_name
msgid "Seo name"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific Types"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "Specific users"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,help:website_appointment.field_appointment_type__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_progress_bar
msgid "Time<span class=\"chevron position-absolute d-block\"/>"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_type_view_form
msgid "Unpublish"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_list_layout
msgid "Use the top button '<b>+ New</b>' to create an appointment type."
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "Users"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointments_cards
msgid "View availability <i class=\"oi oi-arrow-right\"/>"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_appointment
#: model:ir.model,name:website_appointment.model_website
msgid "Website"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_url
msgid "Website URL"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_description
msgid "Website meta description"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_title
msgid "Website meta title"
msgstr ""

#. module: website_appointment
#: model:ir.model.fields,field_description:website_appointment.field_appointment_type__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_booking_resource_description
msgid "available"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "contact us"
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/website_appointment_templates.xml:0
msgid "e.g. \"Technical Demo\""
msgstr ""

#. module: website_appointment
#. odoo-javascript
#: code:addons/website_appointment/static/src/xml/appointment_no_slot.xml:0
msgid "or"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_booking_resource_description
msgid "others)"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_booking_resource_description
msgid "people"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.appointment_booking_resource_description
msgid "resource(s)"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Specify"
msgstr ""

#. module: website_appointment
#: model_terms:ir.ui.view,arch_db:website_appointment.s_online_appointment_options
msgid "⌙ Users"
msgstr ""
