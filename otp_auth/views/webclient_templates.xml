<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="otp_auth.login" name="Login">
        <t t-call="web.login_layout">
            <form class="oe_login_form" role="form" t-attf-action="/web/login" method="post" onsubmit="this.action = '/web/login' + location.hash">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                <div class="mb-3" t-if="databases and len(databases) &gt; 1">
                    <label for="db" class="col-form-label">Database</label>
                    <div t-attf-class="input-group {{'input-group-sm' if form_small else ''}}">
                        <input type="text" name="db" t-att-value="request.db" id="db" t-attf-class="form-control #{'form-control-sm' if form_small else ''}" required="required" readonly="readonly"/>
                        <a role="button" href="/web/database/selector" class="btn btn-secondary">Select <i class="fa fa-database" role="img" aria-label="Database" title="Database"></i></a>
                    </div>
                </div>

                <div class="mb-3 field-login">
                    <label for="login" class="form-label">Email or mobile phone number</label>
                    <input type="text" name="login" t-att-value="login" id="login" t-attf-class="form-control #{'form-control-sm' if form_small else ''}" required="required" autocomplete="username" autofocus="autofocus" autocapitalize="off"/>
                </div>

                <p class="alert alert-danger" t-if="error" role="alert">
                    <t t-esc="error"/>
                </p>
                <p class="alert alert-success" t-if="message" role="status">
                    <t t-esc="message"/>
                </p>

                <div t-attf-class="clearfix oe_login_buttons text-center gap-2 d-grid mb-1 {{'pt-2' if form_small else 'pt-3'}}">
                    <button type="submit" class="btn btn-primary">Continue</button>
                    <!-- btn-link btn-sm-->
                    <button type="submit" name="login_with_pass" value="true" class="btn btn-primary">Login with Password</button>
                    <t t-if="debug">
                        <button type="submit" name="redirect" value="/web/become" class="btn btn-link btn-sm">Log in as superuser</button>
                    </t>
                    <div class="o_login_auth"/>
                </div>

                <input type="hidden" name="redirect" t-att-value="redirect"/>
            </form>
        </t>
    </template>

    <template id="otp_auth.login_auth_otp" name="Login">
        <t t-call="web.login_layout">
            <form class="oe_login_form" role="form" t-attf-action="/web/login" method="post" onsubmit="this.action = '/web/login' + location.hash">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                <input type="hidden" name="db" t-att-value="request.db" id="db"  required="required" />
                <input type="hidden" name="auth_type" value="otp" id="auth_type" required="required" />
                <input type="hidden" name="login" t-att-value="login" id="login" required="required"   autocapitalize="off"/>


                <div class="mb-3">
                    <label for="password" class="form-label">Login with OTP</label>
                    <input type="text" name="password" id="password" t-attf-class="form-control #{'form-control-sm' if form_small else ''}" autocomplete="current-password" t-att-autofocus="'autofocus' if login else None" maxlength="4096"/>
                </div>

                <p class="alert alert-danger" t-if="error" role="alert">
                    <t t-esc="error"/>
                </p>
                <p class="alert alert-success" t-if="message" role="status">
                    <t t-esc="message"/>
                </p>

                <div t-attf-class="clearfix oe_login_buttons text-center gap-1 d-grid mb-1 {{'pt-2' if form_small else 'pt-3'}}">
                    <button type="submit" class="btn btn-primary">Verify</button>
                    <!-- btn-link btn-sm -->
                    <button type="submit" name="login_with_pass" value="true" class="btn btn-primary">I have Password</button>
                    <t t-if="debug">
                        <button type="submit" name="redirect" value="/web/become" class="btn btn-link btn-sm">Log in as superuser</button>
                    </t>
                    <div class="o_login_auth"/>
                </div>

                <input type="hidden" name="redirect" t-att-value="redirect"/>
            </form>
        </t>
    </template>

    <template id="otp_auth.login_auth_password" name="Login">
        <t t-call="web.login_layout">
            <form class="oe_login_form" role="form" t-attf-action="/web/login" method="post" onsubmit="this.action = '/web/login' + location.hash">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                <input type="hidden" name="db" t-att-value="request.db" id="db"  required="required" />
                <input type="hidden" name="auth_type" value="password" id="auth_type" required="required" />
                <input type="hidden" name="login" t-att-value="login" id="login" required="required"   autocapitalize="off"/>

                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" name="password" id="password" t-attf-class="form-control #{'form-control-sm' if form_small else ''}" autocomplete="current-password" t-att-autofocus="'autofocus' if login else None" maxlength="4096"/>
                </div>

                <p class="alert alert-danger" t-if="error" role="alert">
                    <t t-esc="error"/>
                </p>
                <p class="alert alert-success" t-if="message" role="status">
                    <t t-esc="message"/>
                </p>

                <div t-attf-class="clearfix oe_login_buttons text-center gap-1 d-grid mb-1 {{'pt-2' if form_small else 'pt-3'}}">
                    <button type="submit" class="btn btn-primary">Log in</button>
                        <!-- btn-link btn-sm -->
                    <button type="submit" name="login_with_otp" value="true" class="btn btn-primary">I Forgot Password</button>
                    <t t-if="debug">
                        <!-- btn-link btn-sm -->
                        <button type="submit" name="redirect" value="/web/become" class="btn btn-primary">Log in as superuser</button>
                    </t>
                    <div class="o_login_auth"/>
                </div>

                <input type="hidden" name="redirect" t-att-value="redirect"/>
            </form>
        </t>
    </template>
</odoo>
