#################################################################################
# Author      : Webkul Software Pvt. Ltd. (<https://webkul.com/>)
# Copyright(c): 2015-Present Webkul Software Pvt. Ltd.
# All Rights Reserved.
#
#
#
# This program is copyright property of the author mentioned above.
# You can`t redistribute it and/or modify it.
#
#
# You should have received a copy of the License along with this program.
# If not, see <https://store.webkul.com/license.html/>
#################################################################################

from . import models
from . import controllers

def pre_init_check(cr):
    from odoo.service import common
    from odoo.exceptions import UserError
    try:
        import pyotp
    except ImportError as e:
        raise UserError('Installation Error : {} => Kindly install https://pypi.python.org/pypi/pyotp'.format(e))
    version_info = common.exp_version()
    server_serie =version_info.get('server_serie')
    if server_serie!='18.0':raise UserError('Module support Odoo series 18.0 found {}.'.format(server_serie))
