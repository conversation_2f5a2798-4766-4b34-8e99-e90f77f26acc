<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<odoo>
    <data noupdate="0">
        <function  model="ir.default" name="set"
        eval="('res.config.settings', 'otp_time_limit', '120')"/>
        <!-- <function  model="ir.default" name="set"
        eval="('res.config.settings', 'otp_type', '4')"/> -->
        <function  model="ir.default" name="set"
        eval="('res.config.settings', 'signin_auth', True)"/>
        <function  model="ir.default" name="set"
        eval="('res.config.settings', 'signup_auth', True)"/>
        <function  model="ir.default" name="set"
        eval="('res.config.settings', 'otp_content', 'Your secret OTP: &lt;otp&gt;')"/>
    </data>
</odoo>
