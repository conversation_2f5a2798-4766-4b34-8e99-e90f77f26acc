
=============================Website OTP Authentication (ODOO 12.0)================================<br/>
> Functionality to enable O<PERSON> during sign-up <br/>
> Functionality to enable O<PERSON> during sign-in <br/>
> Flexibility to login via OTP or password. <br/>
> Validate valid email during sign-up. <br/>
> OTP validated info at backend view all the validated information and their success status. <br/>
> Check existing using during sign-up. <br/>
> An otp is sent to given email address and only when enter the same otp user are able to register/login. <br/>
> <PERSON><PERSON> can set time of OTP to expire and can set email template. <br/>
> <PERSON><PERSON> can set OTP type (password or text). <br/>
> Cross browser compatible. <br/>
