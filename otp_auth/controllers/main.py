# -*- coding: utf-8 -*-
import pprint
from odoo import http, _,tools
from odoo.http import request
from odoo.addons.web.controllers.home import Home, SIGN_UP_REQUEST_PARAMS
from odoo import http
from odoo.addons.website_sale.controllers.main import WebsiteSale
import pyotp
from odoo.exceptions import ValidationError, UserError,AccessDenied
import logging

_logger=logging.getLogger(__name__)

SIGN_UP_REQUEST_PARAMS = {'db', 'debug', 'token', 'message', 'error', 'scope', 'mode',
                            'redirect', 'redirect_hostname', 'email', 'name', 'partner_id',
                            'password', 'confirm_password', 'city', 'country_id', 'signup_email'}


class MetaAuthSignupHome(Home):
    @http.route(['/otp_auth/generate/otp'], type='json', auth="public", methods=['POST'], website=True)
    def generate_otp(self, **kwargs):
        # email = kwargs.get('email')
        mobile = kwargs.get('email')
        _logger.info("generate_otp")
        user_obj = False
        if mobile :
            if int(kwargs.get('validUser',0))==0:
                user_obj, message = self.checkExistingUser(**kwargs)
            else:
                message = [1, _("Thanks for the registration."), 0]
            if user_obj and message[0] != 0:
                otpdata = self.getOTPData(user_obj.partner_id)
                otp = otpdata[0]
                otp_time = otpdata[1]
                self.sendOTP(otp, user_obj)
                message = [1, _("OTP has been sent to mobile : {}".format(mobile)), otp_time]
        else:
            message = [0, _("Please enter mobile number or email ID"), 0]
        return message
    
    
    
    
    
    
    def checkExistingUser(self, **kwargs):
        # email = kwargs.get('email', None)
        login = False
        if kwargs.get('login',False):
            login = kwargs.get('login')
        elif kwargs.get('mobile',False):
            login = kwargs.get('mobile')
        elif kwargs.get('email',False):
            login = kwargs.get('email')
        if not login:
            message = [0, _("Please provide valid Login Email or Mobile Number."), 0]
            return False, message
        
        user_obj = request.env["res.users"].sudo().search(["|",("login", "=", login),("mobile","=",login)], limit=1)
        message = [1, _("Thanks for the registration."), 0]
        _logger.warning("{}".format(user_obj))
        if not user_obj:
            message = [0, _("No user is found registered using this email or mobile number."), 0]
            return False, message
        return user_obj, message
    
    
    
    
    
    def sendOTP(self, otp, user_obj):
        user_name = user_obj.name
        # email = kwargs.get('email')
        mobile = user_obj.mobile
        request.env['send.otp'].email_send_otp(user_name, otp, mobile)
        return True
    
    
    
    
    @http.route(['/verify/otp'], type='json', auth="public", methods=['POST'], website=True)
    def verify_otp(self, otp=False):
        totp = int(request.session.get('otpobj'))
        if otp.isdigit():
            return True if totp==int(otp) else False
        else:
            return False
    
    
    
    
    
    @http.route(website=True, auth="public", sitemap=False)
    def web_login(self, redirect=None, *args, **kw):
        # response = super(MetaAuthSignupHome, self).web_login(redirect=redirect, *args, **kw)
        signin_auth = (
                request.env["ir.default"]
                .sudo()
                ._get("res.config.settings", "signin_auth")
            )
        print(f"login params ---------------------------------- {pprint.pformat(request.params)}")
        if not signin_auth:
            return super().web_login(redirect=redirect, *args, **kw)
        
        request.params['login_success'] = False
        if request.httprequest.method == 'GET' and redirect and request.session.uid:
            return request.redirect(redirect)

        # simulate hybrid auth=user/auth=public, despite using auth=none to be able
        # to redirect users when no db is selected - cfr ensure_db()
        if request.env.uid is None:
            if request.session.uid is None:
                # no user -> auth=public with specific website public user
                request.env["ir.http"]._auth_method_public()
            else:
                # auth=user
                request.update_env(user=request.session.uid)

        values = {k: v for k, v in request.params.items() if k in SIGN_UP_REQUEST_PARAMS}
        try:
            values['databases'] = http.db_list()
        except AccessDenied:
            values['databases'] = None

        if not tools.config['list_db']:
            values['disable_database_manager'] = True
        
        if 'login' not in values and request.params.get('login',False):
            values['login'] = request.params.get('login',False)

        if 'error' in request.params and request.params.get('error') == 'access':
                values['error'] = _('Only employees can access this database. Please contact the administrator.')
                
        print(f'login Values------------------------------- {values}')
        if request.httprequest.method == 'POST':
            if not request.params.get('login',False):
                print("level 1")
                values['auth_type'] = 'otp'
                return request.render('otp_auth.login', values)
            
            if request.params.get('login_with_otp'):
                print("level 2")
                user_data, msg = self.checkExistingUser(**kw)
                if user_data and msg[0] == 1:
                    print("level 2.1")

                    otp_msg = user_data.partner_id.send_otp()
                    if not otp_msg[0]:
                        print("level 2.1.1")

                        if otp_msg[2] == 'warning':
                            values['message'] = otp_msg[1]
                        else:
                            values['error'] = otp_msg[1]
                        # values.pop('login')
                        print(f" otp sent msg {otp_msg}")
                        return request.render('otp_auth.login', values)
                    
                values['login'] = request.params.get('login')
                return request.render('otp_auth.login_auth_otp', values)
            
            if request.params.get('login_with_pass'):
                print("level 3")
                values['auth_type'] = 'password'
                return request.render('otp_auth.login_auth_password', values) 
            
            if request.params.get('login',False) and not request.params.get('password',False):
                print("level 4")
                #auth method is not selected then return the user login input screen
                try:
                    user_data, msg = self.checkExistingUser(**kw)
                    if user_data and msg[0] == 1:
                        print("level 4.1")
                        otp_msg = user_data.partner_id.send_otp()
                        if not otp_msg[0]:
                            print("level 4.1.1")
                            if otp_msg[2] == 'warning':
                                values['message'] = otp_msg[1]
                            else:
                                values['error'] = otp_msg[1]
                            return request.render('otp_auth.login', values)
                        
                        return request.render('otp_auth.login_auth_otp', values)
                    else:
                        print("level 4.2")
                        values['error'] = msg[1]
                except Exception as e:                   
                        values['error'] = str(e)

                return request.render('otp_auth.login', values)

            try:
                credential = {'login': request.params['login'], 'password': request.params['password'], 'type': 'password'}
                print("line 194 .................................................")
                uid = request.session.authenticate(request.db, credential)
                print("line 196 uid.................................................")
                request.params['login_success'] = True
                return request.redirect(self._login_redirect(uid['uid'], redirect=redirect))
            except AccessDenied as e:
                if e.args == AccessDenied().args:
                    values['error'] = _("Wrong login/password")
                else:
                    values['error'] = e.args[0]

        response = request.render('otp_auth.login', values)
        response.headers['X-Frame-Options'] = 'SAMEORIGIN'
        response.headers['Content-Security-Policy'] = "frame-ancestors 'self'"
        return response

        
        totp = request.session.get('otploginobj')
        password = kw.get('password','***')
        if kw.get('radio-otp')=='radiotp' :
            request.session['radio-otp']='radiotp'
            if totp and totp.isdigit() and password.isdigit():
                if int(totp) != int(password):
                    values['error'] = _("Incorrect OTP")
            else:
                values['error'] = _("Incorrect OTP")
        else:
            request.session['radio-otp']='radiotp'
        _logger.warning("web_login response ----->>>>> {}".format(response))
        return response
    
    
    
    
    
    @http.route('/web/reset_password', type='http', auth='public', website=True, sitemap=False)
    def web_auth_reset_password(self, *args, **kw):
        request.session['radio-otp']=None
        return super(MetaAuthSignupHome, self).web_auth_reset_password(*args, **kw)
    
    
    
    
    
    @http.route('/web/signup', type='http', auth='public', website=True, sitemap=False)
    def web_auth_signup(self, *args, **kw):
        request.session['radio-otp']='radiopwd'
        if not kw.get('login'):
            return super(MetaAuthSignupHome, self).web_auth_signup(*args, **kw)
        if kw.get('otp'):
            totp = int(request.session.get('otpobj'))
            if totp == int(kw.get('otp')):
                return super(MetaAuthSignupHome, self).web_auth_signup(*args, **kw)
            else:
                qcontext = self.get_auth_signup_qcontext()
                response = request.render('auth_signup.signup', qcontext)
                response.headers['X-Frame-Options'] = 'DENY'
                return response
        else:
            return super(MetaAuthSignupHome, self).web_auth_signup(*args, **kw)
    
    
    
    
    def get_auth_signup_qcontext(self):
        """ Shared helper returning the rendering context for signup and reset password """
        _logger.warning("Entered get_auth_signup_qcontext in otp_auth")
        
        qcontext = super(MetaAuthSignupHome, self).get_auth_signup_qcontext()
        mobile = request.params.get('mobile')
        qcontext.update({
            "mobile" : mobile
        })
        return qcontext
    
    
    
    
    @http.route(['/send/otp'], type='json', auth="public", methods=['POST'], website=True)
    def send_otp(self, **kwargs):
        _logger.info("send_otp")

        phone = kwargs.get('email')
        if phone:
            # Get mobile number 
            user_data, msg = self.checkExistingUser(**kwargs)
            print("Got phone -------->", phone)
            if user_data and user_data.login:     
                otpdata = kwargs.get('otpdata') if kwargs.get('otpdata') else self.getOTPData(user_data.partner_id)
                otp = otpdata[0]
                otp_time = otpdata[1]
                # Send Login Otp
                request.env['send.otp'].email_send_otp(False, otp, user_data.mobile)
                message = {"email":{'status':1, 'message':_("OTP has been sent."), 'otp_time':otp_time, 'login':phone}}
            else:
                message = {"email":{'status':0, 'message':_("User account does not exist. Please signup for an account else try different email or mobile number."), 'otp_time':0, 'login':phone}}
        else:
            message = {"email":{'status':0, 'message':_("Enter an email or phone number."), 'otp_time':0, 'login':False}}
        return message

    def getOTPData(self,partner_id):
        otp, otp_time = request.env['meta.otp.auth'].sudo().get_new_otp(partner_id )

        request.session['otploginobj'] = otp
        request.session['otpobj'] = otp
        return [otp, otp_time]
    
    
    
    
    def _prepare_signup_values(self, qcontext):
        values = super()._prepare_signup_values(qcontext)
        if qcontext.get('mobile'):
            values['mobile'] = qcontext.get('mobile')
            values['phone'] = qcontext.get('mobile')
        return values
    
    
    
    @http.route(['/get/login'], type='json', auth="public", methods=['POST'], website=True)
    def get_login(self, **kwargs):
        email = kwargs.get('email')
        if email:
            # Get mobile number 
            # user_data = request.env["res.users"].sudo().search([('mobile', '=', email)],limit=1)
            user_data, msg = self.checkExistingUser(**kwargs)

            if user_data and user_data.login:      
                # Send Login Otp
                message = {"email":{'status':1, 'message':_("OTP has been sent to : {}.".format(email)), 'login':user_data.login}}
            else:
                message = {"email":{'status':0, 'message':_("User account does not exist. Please signup for an account else try different email or mobile number."), 'otp_time':0, 'login':email}}
        else:
            message = {"email":{'status':0, 'message':_("Enter an email or phone number."), 'otp_time':0, 'login':False}}
        return message
    
    
    
    @http.route(['/otp/verification'], type='http', auth="public", website=True)
    def otp_verifcation(self, redirect=None):
        partner = request.env.user.partner_id
        if not partner:
            return request.redirect('/web/login')
        resp = partner.send_otp()         
        redirect= redirect or '/my'
        return request.render('otp_auth.otp_verify_after_signup', {'partner':partner, 'resp':resp, 'redirect':redirect})
    
    
    
    
    @http.route(['/otp/verify'], type='http', auth="public", methods=['POST'], website=True)
    def verify_otp(self, redirect=None, otp=False, **post):
        totp = post.get('otpin')
        partner_id = int(post.get('partner_id'))
        partner = request.env.user.partner_id
        resp = request.env['meta.otp.auth'].verify_otp_pin(partner_id, totp)
        redirect= redirect or '/my'
        if resp[0] == True and  partner_id == partner.id:
            return request.redirect(redirect)
        else:
            return request.render('otp_auth.otp_verify_after_signup', {'partner':partner,'resp':resp, 'redirect':redirect})
    
    
    
    
class OtpAuthWebsiteSale(WebsiteSale):
    @http.route(['/shop/checkout'], type='http', auth="public", website=True, sitemap=False)
    def shop_checkout(self, **post):
        partner = request.env.user.partner_id
        # if partner.is_public:
        #     return request.redirect("/shop/address")

        # else:
        
        if not partner:
            return request.redirect("/web/signup")
            # elif not partner.otp_varified:
            #     return request.redirect("/otp/verification?redirect={}".format('/shop/checkout'))

        res = super(OtpAuthWebsiteSale, self).shop_checkout()        
        return res