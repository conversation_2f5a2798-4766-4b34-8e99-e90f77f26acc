# -*- coding: utf-8 -*-
import logging
import requests
import base64
import json
import string
import random
from odoo import api, models, _, SUPERUSER_ID
from odoo.exceptions import ValidationError
import pprint
import hashlib
_logger = logging.getLogger(__name__)



class SendOtp(models.TransientModel):
    _name = "send.otp"

    # @api.multi
    def email_send_otp(self, userName, otp, mobile):
        userObj = (
            self.env["res.users"]
            .sudo()
            .search(["|", ("login", "=", mobile), ("mobile", "=", mobile)], limit=1)
        )

        if not userName and userObj:
            userName = userObj.name

        if userObj and userObj.partner_id and userObj.partner_id.email_normalized:
            try:
                otp_obj = (
                    self.env["meta.otp.auth"]
                    .sudo()
                    .search([("partner_id", "=", userObj.partner_id.id)], limit=1)
                )

                template = self.env.ref("otp_auth.email_template_user_otp").sudo()
                logging.info(
                    f"to send otp {otp} ------ Sending OTP {otp_obj.otp} email -----------  via template{template}"
                )
                
                template.send_mail(
                    otp_obj.id,
                    force_send=True,
                    email_layout_xmlid="mail.mail_notification_light",
                )
            except Exception as e:
                logging.exception(f"Error While sending OTP Email {e}")
                pass

        if mobile:
            logging.info(
                    f"to send otp {otp} ------ to mobile ----------- {mobile}"
                )
            sms_provider = (
                self.env["ir.default"]
                .sudo()
                ._get("res.config.settings", "otp_sms_provider")
            )
            sms_text = (
                self.env["ir.default"]
                .sudo()
                ._get("res.config.settings", "otp_content")
                .replace("<otp>", f"{str(otp)}")
            )
            logging.info(f"sms_text:------------------------ {sms_text}")

            if sms_provider == "alpha":
                _logger.info(f"sms_provider ---------------------------- {sms_provider}")
                
                alpha_api_key = self.env["ir.default"].sudo()._get("res.config.settings", "alpha_api_key")
                
                # Prepare request parameters
                params = {
                    "api_key": alpha_api_key,
                    "msg": sms_text,
                    "to": mobile
                }
                _logger.info(f"params:------------------------ {params}")
                response = requests.post(
                    'https://api.sms.net.bd/sendsms',
                    params= params
                )
                api_response = pprint.pformat(response.content)
                _logger.info(f"response:------------------------ {api_response}")
        
        return True
