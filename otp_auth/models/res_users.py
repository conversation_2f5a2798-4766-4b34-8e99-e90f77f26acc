# -*- coding: utf-8 -*-

import re
from datetime import datetime
from odoo.tools import email_normalize
from odoo import api,fields, models, _
from odoo.http import request
from odoo.exceptions import AccessDenied, UserError
from odoo.addons.auth_signup.models.res_partner import SignupError, now
import logging
_logger = logging.getLogger(__name__)

def is_email(string):
        try:
            email_normalize(string)
            return True
        except ValueError:
            return False

def is_phone_number(string):
    print(f'is_phone_number  string {string}')

    # Remove any non-digit characters from the string
    phone = re.sub(r'\D', '', string)

    # Check if the resulting string matches one of the specified formats
    if re.match(r'^\+?8801[3-9]\d{8}$', phone):
        # Remove the country code and leading zero from the phone number
        phone = phone[-10:]
    elif re.match(r'^\+?880-1[3-9]\d{3}-\d{5}$', phone):
        # Remove the country code, hyphens, and leading zero from the phone number
        phone = phone[-11:-8] + phone[-7:-4] + phone[-3:]
    elif re.match(r'^01[3-9]\d{2}-\d{3}-\d{4}$', phone):
        # Remove the hyphens from the phone number
        phone = phone[:3] + phone[4:7] + phone[8:]
    elif re.match(r'^01[3-9]\d{8}$', phone):
        # The phone number is already in the desired format
        pass
    else:
        # The phone number does not match any of the specified formats
        return False

    # Return the sanitized phone number
    print(f'is_phone_number {phone}')
    return phone





class MetaResPartner(models.Model):
    _inherit = 'res.partner'

    otp_varified = fields.Boolean(
        string='Otp varified', default=False
        )
    
    def send_otp(self):
        send_otp_model = self.env['send.otp']
        otp_model = self.env["meta.otp.auth"]
        for partner in self:
            email = partner.email
            mobile = partner.mobile or partner.phone
            otp = otp_model.search([['partner_id','=', partner.id]], order='create_date desc', limit=1)
            
            if not otp or otp.is_expired():
                otp = otp_model.get_new_otp(partner)
                if mobile:
                    send_otp_model.email_send_otp(partner.name or 'User', otp[0], mobile)
                    resp = [True, 'OTP Sent, Valid for {} seconds'.format(otp[1]), 'success']
                elif email:
                    send_otp_model.email_send_otp(partner.name or 'User', otp[0], email)
                    resp = [True, 'OTP Sent, Valid for {} seconds'.format(otp[1]), 'success']
                else:
                    resp = [False, 'Unable to send OTP. Mobile Phone number not found.', 'error']
            else:
                resp = [True, 'Already sent an OTP. Please Try after {} seconds.'.format(int(otp.expire_time -(datetime.now()- otp.create_date).total_seconds())), 'warning']
            return resp







class Users(models.Model):
    _inherit = 'res.users'

    # mobile = fields.Char(string="Mobile Number", default="")

    @api.model
    def _check_credentials(self, password, env):
        if request.params.get('auth_type', 'password') == 'password':
            return super()._check_credentials(password, env)
        
        verify_otp= self.env["meta.otp.auth"].sudo().verify_otp(self.partner_id.id, password)
        if verify_otp:
            return {
                'uid': self.env.user.id,
                'auth_method': 'password',
                'mfa': 'default',
            }

        if not verify_otp[0]:
            raise AccessDenied()
    
    
    
    
    @api.model_create_multi
    def create(self, val_list):
        for vals in val_list:
            _logger.warning("User create Values {}".format(vals))
            if isinstance(vals['login'], (tuple, list)):
                vals['login'] = vals['login'][0]
            else: vals['login'] = vals['login']
            valid_phone = is_phone_number(vals['login'])
            if valid_phone:
                vals['phone'] = valid_phone
                vals['mobile'] = valid_phone
                vals.pop('email',False)
        return super(Users, self).create(val_list)
    
    
    
    
    def action_reset_password(self):
        """ create signup token for each user, and send their signup url by email """
        if self.env.context.get('install_mode', False):
            return
        if self.filtered(lambda user: not user.active):
            raise UserError(_("You cannot perform this action on an archived user."))
        # prepare reset password signup
        create_mode = bool(self.env.context.get('create_user'))

        # no time limit for initial invitation, only for reset password
        expiration = False if create_mode else now(days=+1)

        self.mapped('partner_id').signup_prepare(signup_type="reset", expiration=expiration)

        # send message to users with their signup url
        for user in self:
            if not user.email:
                raise UserError(_("Cannot send email: user %s has no email address.", user.name))
            mobile = user.email
            sms_text = self.env['ir.default'].sudo()._get('res.config.settings', 'reset_pass_content').replace('<name>', user.name)
            self.env['send.sms'].send_sms(mobile, sms_text)
            _logger.warning("Sending Non Cash Payment SMS-------->")

