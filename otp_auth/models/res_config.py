# -*- coding: utf-8 -*-
#################################################################################
#
#   Copyright (c) 2015-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>)
#   See LICENSE file for full copyright and licensing details.
#   If not, see <https://store.webkul.com/license.html/>
#
#################################################################################

from odoo import api, fields, models, _


class WebsiteOTPSettings(models.TransientModel):
    _inherit = "res.config.settings"

    signin_auth = fields.Boolean(string="Sign-in OTP Authentication")
    signup_auth = fields.Boolean(string="Sign-up OTP Authentication")

    otp_time_limit = fields.Integer("OTP Time Limit (sec)", help="OTP expiry time")

    otp_content = fields.Text("OTP SMS Content")
    otp_sms_provider = fields.Selection(
        [
            ("alpha", "Alpha")
        ],
        string="OTP SMS Provider",
    )
    
    alpha_api_key = fields.Char("Alpha API Key")

    # @api.multi
    def set_values(self):
        super(WebsiteOTPSettings, self).set_values()
        IrDefault = self.env["ir.default"].sudo()
        IrDefault.set("res.config.settings", "signin_auth", self.signin_auth)
        IrDefault.set("res.config.settings", "signup_auth", self.signup_auth)
        IrDefault.set("res.config.settings", "otp_time_limit", self.otp_time_limit)
        IrDefault.set("res.config.settings", "otp_content", self.otp_content)
        IrDefault.set("res.config.settings", "otp_sms_provider", self.otp_sms_provider)
        
        IrDefault.set("res.config.settings", "alpha_api_key", self.alpha_api_key)

        return True

    # @api.multi
    def get_values(self):
        res = super(WebsiteOTPSettings, self).get_values()
        IrDefault = self.env["ir.default"].sudo()
        res.update(
            {
                "signin_auth": IrDefault._get(
                    "res.config.settings", "signin_auth", self.signin_auth
                ),
                "signup_auth": IrDefault._get(
                    "res.config.settings", "signup_auth", self.signup_auth
                ),
                "otp_content": IrDefault._get(
                    "res.config.settings", "otp_content", self.otp_content
                ),
                "otp_sms_provider": IrDefault._get(
                    "res.config.settings", "otp_sms_provider", self.otp_sms_provider
                ),
                
                "alpha_api_key": IrDefault._get(
                    "res.config.settings", "alpha_api_key", self.alpha_api_key
                ),
                
            }
        )
        return res
