<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- NVR Server List View -->
    <record id="view_nvr_server_list" model="ir.ui.view">
        <field name="name">nvr.server.list</field>
        <field name="model">nvr.server</field>
        <field name="arch" type="xml">
            <list string="NVR Servers">
                <field name="name"/>
                <field name="host"/>
                <field name="port"/>
                <field name="state" widget="statusbar" statusbar_visible="draft,tested,error"/>
                <field name="last_connection"/>
                <field name="log_count" widget="statinfo"/>
            </list>
        </field>
    </record>

    <!-- NVR Server Form View -->
    <record id="view_nvr_server_form" model="ir.ui.view">
        <field name="name">nvr.server.form</field>
        <field name="model">nvr.server</field>
        <field name="arch" type="xml">
            <form string="NVR Server">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="draft,tested,error"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="test_connection" type="object" class="oe_stat_button" icon="fa-plug">
                            <field name="state" widget="statinfo" string="Test Connection"/>
                        </button>
                        <button name="fetch_logs" type="object" class="oe_stat_button" icon="fa-download">
                            <div>Fetch Logs</div>
                        </button>
                        <button name="action_view_logs" type="object" class="oe_stat_button" icon="fa-list">
                            <field name="log_count" widget="statinfo" string="Logs"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="active" widget="boolean_toggle" options="{'no_open': True}"/>
                            <field name="host"/>
                            <field name="port"/>
                            <field name="username"/>
                            <field name="password" password="True"/>
                            <field name="verify_ssl"/>
                        </group>
                        <group>
                            <field name="log_type_ids" widget="many2many_tags" options="{'no_create_edit': True}"/>
                            <field name="last_connection" readonly="1"/>
                            <field name="log_count" readonly="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action to open NVR Server -->
    <record id="action_nvr_server" model="ir.actions.act_window">
        <field name="name">NVR Servers</field>
        <field name="res_model">nvr.server</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No NVR Server found. Let's create one!
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_nvr_root" name="NVR" sequence="10" />
    <menuitem id="menu_nvr_servers" name="NVR Servers" parent="menu_nvr_root" action="action_nvr_server" sequence="10" />
</odoo>
