<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- NVR Log Type List View -->
    <record id="view_nvr_log_type_list" model="ir.ui.view">
        <field name="name">nvr.log.type.list</field>
        <field name="model">nvr.log.type</field>
        <field name="arch" type="xml">
            <list string="NVR Log Types">
                <field name="name"/>
                <field name="code"/>
                <field name="active" invisible="1"/>
                <field name="notify_users" invisible="1"/>
                <field name="log_count" widget="statinfo" string="Logs"/>
            </list>
        </field>
    </record>

    <!-- NVR Log Type Search View -->
    <record id="view_nvr_log_type_search" model="ir.ui.view">
        <field name="name">nvr.log.type.search</field>
        <field name="model">nvr.log.type</field>
        <field name="arch" type="xml">
            <search string="NVR Log Types">
                <field name="name"/>
                <field name="code"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Notifications Enabled" name="notify" domain="[('notify_users', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Notification Type" name="group_notification_type" context="{'group_by': 'notification_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- NVR Log Type Form View -->
    <record id="view_nvr_log_type_form" model="ir.ui.view">
        <field name="name">nvr.log.type.form</field>
        <field name="model">nvr.log.type</field>
        <field name="arch" type="xml">
            <form string="NVR Log Type">
                <header>
                    <button name="action_view_logs" type="object" class="oe_stat_button" icon="fa-list" string="View Logs">
                        <field name="log_count" widget="statinfo" string="Logs"/>
                    </button>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="active" widget="boolean_toggle"/>
                            <field name="notify_users" widget="boolean_toggle"/>
                            <field name="user_ids" widget="many2many_tags" attrs="{'invisible': [('notify_users', '=', False)]}" options="{'no_create_edit': true}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action to open NVR Log Types -->
    <record id="action_nvr_log_type" model="ir.actions.act_window">
        <field name="name">NVR Log Types</field>
        <field name="res_model">nvr.log.type</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_nvr_log_type_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No log types found. Let's create one!
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_nvr_log_types" name="Log Types" parent="menu_nvr_root" action="action_nvr_log_type" sequence="30"/>
</odoo>
