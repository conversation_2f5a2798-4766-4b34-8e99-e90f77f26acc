<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- NVR Log List View -->
    <record id="view_nvr_log_list" model="ir.ui.view">
        <field name="name">nvr.log.list</field>
        <field name="model">nvr.log</field>
        <field name="arch" type="xml">
            <list string="NVR Logs" decoration-success="is_notified" decoration-warning="not is_notified">
                <field name="server_id" groups="base.group_system"/>
                <field name="log_time"/>
                <field name="log_type"/>
                <field name="log_subtype"/>
                <field name="details"/>
                <field name="is_notified" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- NVR Log Search View -->
    <record id="view_nvr_log_search" model="ir.ui.view">
        <field name="name">nvr.log.search</field>
        <field name="model">nvr.log</field>
        <field name="arch" type="xml">
            <search string="NVR Logs">
                <field name="server_id"/>
                <field name="log_type"/>
                <field name="log_subtype"/>
                <field name="log_time"/>
                <field name="details"/>
                <filter string="Unnotified" name="unnotified" domain="[('is_notified', '=', False)]"/>
                <filter string="Today" name="today" domain="[('log_time', '&gt;=', context_today().strftime('%Y-%m-%d 00:00:00')), ('log_time', '&lt;=', context_today().strftime('%Y-%m-%d 23:59:59'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Server" name="group_server" context="{'group_by': 'server_id'}"/>
                    <filter string="Type" name="group_type" context="{'group_by': 'log_type'}"/>
                    <filter string="Subtype" name="group_subtype" context="{'group_by': 'log_subtype'}"/>
                    <filter string="Day" name="group_date" context="{'group_by': 'log_time:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- NVR Log Form View -->
    <record id="view_nvr_log_form" model="ir.ui.view">
        <field name="name">nvr.log.form</field>
        <field name="model">nvr.log</field>
        <field name="arch" type="xml">
            <form string="NVR Log">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_raw_data" type="object" class="oe_stat_button" icon="fa-code">
                            <div>View Raw Data</div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="log_type_display" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="server_id" options="{'no_open': True}"/>
                            <field name="log_time"/>
                            <field name="is_notified" readonly="1"/>
                        </group>
                        <group>
                            <field name="log_type"/>
                            <field name="log_subtype"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Details">
                            <field name="details" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Raw Data Form View -->
    <record id="view_nvr_log_raw_data_form" model="ir.ui.view">
        <field name="name">nvr.log.raw.data.form</field>
        <field name="model">nvr.log</field>
        <field name="arch" type="xml">
            <form string="Raw Log Data">
                <sheet>
                    <div class="oe_title">
                        <h1>Raw Log Data</h1>
                    </div>
                    <group>
                        <field name="raw_data" nolabel="1" widget="json_field"/>
                    </group>
                    <footer>
                        <button string="Close" class="btn-primary" special="cancel"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action to open NVR Logs -->
    <record id="action_nvr_log" model="ir.actions.act_window">
        <field name="name">NVR Logs</field>
        <field name="res_model">nvr.log</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_nvr_log_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No logs found. Try fetching logs from an NVR server.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_nvr_logs" name="Logs" parent="menu_nvr_root" action="action_nvr_log" sequence="20"/>
</odoo>
