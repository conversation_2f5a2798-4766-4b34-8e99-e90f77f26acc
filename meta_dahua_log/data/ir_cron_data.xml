<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Cron job to fetch logs from NVR servers every minute -->
    <record id="ir_cron_fetch_nvr_logs" model="ir.cron">
        <field name="name">NVR: Fetch Logs</field>
        <field name="model_id" ref="model_nvr_server"/>
        <field name="state">code</field>
        <field name="code">model._cron_fetch_logs()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">minutes</field>
        <field name="active" eval="True"/>
    </record>
</odoo>
