id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_nvr_server_user,nvr.server.user,model_nvr_server,base.group_user,1,1,1,0
access_nvr_server_manager,nvr.server.manager,model_nvr_server,base.group_system,1,1,1,1
access_nvr_log_user,nvr.log.user,model_nvr_log,base.group_user,1,0,0,0
access_nvr_log_manager,nvr.log.manager,model_nvr_log,base.group_system,1,1,1,1
access_nvr_log_type_user,nvr.log.type.user,model_nvr_log_type,base.group_user,1,0,0,0
access_nvr_log_type_manager,nvr.log.type.manager,model_nvr_log_type,base.group_system,1,1,1,1
access_nvr_log_notification_user,nvr.log.notification.user,model_nvr_log_notification,base.group_user,1,1,1,1
