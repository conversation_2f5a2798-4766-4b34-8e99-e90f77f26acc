from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.tools import format_datetime
import json
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)

class NVRLog(models.Model):
    _name = 'nvr.log'
    _description = 'NVR Log Entry'
    _order = 'log_time desc'
    _rec_name = 'details'

    server_id = fields.Many2one('nvr.server', string='NVR Server', required=True, ondelete='cascade')
    log_type = fields.Char(string='Log Type', index=True)
    log_subtype = fields.Char(string='Log Subtype', index=True)
    log_time = fields.Datetime(string='Log Time', index=True)
    details = fields.Text(string='Details')
    raw_data = fields.Text(string='Raw Data', help='Raw JSON data from NVR')
    is_notified = fields.Boolean(string='Notified', default=False, index=True)
    notification_ids = fields.One2many('nvr.log.notification', 'log_id', string='Notifications')
    log_type_display = fields.Char(string='Log Type Display', compute='_compute_log_type_display')

    def name_get(self):
        result = []
        for log in self:
            name = f"{log.log_type or ''}: {log.details or ''}"
            if len(name) > 50:
                name = name[:47] + '...'
            result.append((log.id, name))
        return result

    def _compute_log_type_display(self):
        for log in self:
            log.log_type_display = f"{log.log_type or ''}{'.' + log.log_subtype if log.log_subtype else ''}"

    def get_log_type_name(self):
        self.ensure_one()
        return f"{self.log_type or ''}{'.' + self.log_subtype if self.log_subtype else ''}"

    def action_view_raw_data(self):
        self.ensure_one()
        return {
            'name': _('Raw Log Data'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'nvr.log',
            'res_id': self.id,
            'view_id': self.env.ref('meta_dahua_log.view_nvr_log_raw_data_form').id,
            'target': 'new',
            'flags': {'mode': 'readonly'},
        }
        
    def _send_notifications(self):
        """Send inbox notifications for this log based on log type configuration"""
        self.ensure_one()
        
        # Find log type configuration
        log_type = self.log_type
        log_subtype = self.log_subtype or ''
        log_type_code = f"{log_type}.{log_subtype}" if log_subtype else log_type
        
        # Find matching log type configurations
        log_type_configs = self.env['nvr.log.type'].search([
            '|',
            ('code', '=', log_type_code),
            '&',
                ('code', '=', log_type),
                ('code', 'not like', '.')
        ])
        
        for config in log_type_configs:
            if not config.notify_users or not config.user_ids:
                continue
                
            # Prepare notification content
            subject = _("NVR Log: %s") % (config.name or log_type_code)
            body = f"""
                <p><strong>NVR Server:</strong> {self.server_id.name or 'N/A'}</p>
                <p><strong>Time:</strong> {format_datetime(self.env, self.log_time) if self.log_time else 'N/A'}</p>
                <p><strong>Type:</strong> {self.log_type or 'N/A'}</p>
                <p><strong>Subtype:</strong> {self.log_subtype or 'N/A'}</p>
                <p><strong>Details:</strong> {self.details or 'N/A'}</p>
                <p><a href="/web#id={self.id}&model=nvr.log&view_type=form" class="btn btn-primary">View Log</a></p>
            """
            
            # Create inbox notification for each user
            for user in config.user_ids:
                self._create_inbox_notification(user, subject, body)
        
        return True
    
    def _create_inbox_notification(self, user, subject, body):
        """Create an inbox notification for the user"""
        try:
            self.env['mail.message'].create({
                'message_type': 'notification',
                'body': body,
                'subject': subject,
                'partner_ids': [(6, 0, [user.partner_id.id])],
                'model': 'nvr.log',
                'res_id': self.id,
                'record_name': f"{self.log_type or 'Log'} {self.id}",
                'subtype_id': self.env.ref('mail.mt_comment').id,
            })
            
            # Create notification record
            self.env['nvr.log.notification'].create({
                'log_id': self.id,
                'user_id': user.id,
                'notification_type': 'inbox',
            })
            
        except Exception as e:
            _logger.error("Failed to create inbox notification: %s", str(e))
    
    @api.model_create_multi
    def create(self, vals_list):
        logs = super().create(vals_list)
        for log in logs:
            log._send_notifications()
        return logs

class NVRLogNotification(models.Model):
    _name = 'nvr.log.notification'
    _description = 'NVR Log Notification'
    _order = 'create_date desc'

    log_id = fields.Many2one('nvr.log', string='Log Entry', required=True, ondelete='cascade')
    user_id = fields.Many2one('res.users', string='User', required=True)
    notification_type = fields.Selection([
        ('email', 'Email'),
        ('inbox', 'Inbox'),
        ('both', 'Both')
    ], string='Notification Type', required=True)
    notification_date = fields.Datetime(string='Notification Date', default=fields.Datetime.now)
    is_read = fields.Boolean(string='Read', default=False)

    def mark_as_read(self):
        self.write({'is_read': True})
