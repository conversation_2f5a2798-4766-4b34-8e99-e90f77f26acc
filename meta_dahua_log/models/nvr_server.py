import hashlib
import logging
import requests
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

_logger = logging.getLogger(__name__)

class NVRServer(models.Model):
    _name = 'nvr.server'
    _description = 'NVR Server Configuration'
    _order = 'name'

    name = fields.Char(string='Name', required=True)
    active = fields.Boolean(string='Active', default=True)
    host = fields.Char(string='Host', required=True, help="NVR IP address or hostname")
    port = fields.Integer(string='Port', default=80, help="HTTP port (default: 80)")
    username = fields.Char(string='Username', required=True)
    password = fields.Char(string='Password', required=True)
    verify_ssl = fields.Boolean(string='Verify SSL', default=False,
                              help="Verify SSL certificate")
    state = fields.Selection([
        ('draft', 'Not Tested'),
        ('tested', 'Tested'),
        ('error', 'Error')
    ], string='Status', default='draft', readonly=True)
    last_connection = fields.Datetime('Last Connection', readonly=True)
    log_count = fields.Integer(compute='_compute_log_count', string='Logs')
    log_type_ids = fields.Many2many('nvr.log.type', string='Log Types to Fetch')

    _sql_constraints = [
        ('name_uniq', 'UNIQUE(name)', 'NVR name must be unique!'),
    ]

    def _compute_log_count(self):
        for server in self:
            server.log_count = self.env['nvr.log'].search_count([('server_id', '=', server.id)])

    @api.model
    def _get_dahua_rpc(self, server):
        """Return a DahuaRPC instance for the given server"""
        return DahuaRpc(server.host, server.username, server.password, server.port)

    def test_connection(self):
        """Test NVR connection"""
        self.ensure_one()
        try:
            rpc = self._get_dahua_rpc(self)
            if rpc.login():
                self.write({
                    'state': 'tested',
                    'last_connection': fields.Datetime.now(),
                })
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Connection to NVR successful!'),
                        'sticky': False,
                        'type': 'success',
                    }
                }
        except Exception as e:
            _logger.error("NVR Connection Error: %s", str(e))
            self.write({'state': 'error'})
            raise UserError(_('Connection Test Failed! %s') % str(e))

    def fetch_logs(self):
        """Fetch logs from NVR"""
        self.ensure_one()
        Log = self.env['nvr.log']
        rpc = self._get_dahua_rpc(self)

        if not rpc.login():
            _logger.error("Failed to login to NVR %s", self.name)
            return False

        try:
            # Get log types to fetch
            log_types = self.log_type_ids.mapped('code')
            if not log_types:
                _logger.warning("No log types configured for NVR %s", self.name)
                return False

            # Set time range (last 5 minutes)
            end_time = fields.Datetime.now()
            start_time = fields.Datetime.subtract(end_time, minutes=5)

            # Convert to Dahua timestamp format
            start_ts = int(start_time.timestamp())
            end_ts = int(end_time.timestamp())

            # Fetch logs
            token = rpc.start_log_find(start_ts, end_ts, log_types)
            count = rpc.get_log_count(token)

            if count > 0:
                logs = rpc.get_log_items(token, 0, count)
                for log in logs:
                    Log.create({
                        'server_id': self.id,
                        'log_type': log.get('Type', ''),
                        'log_subtype': log.get('SubType', ''),
                        'log_time': fields.Datetime.fromtimestamp(log.get('Time', 0)),
                        'details': log.get('Details', ''),
                        'raw_data': log,
                    })

            rpc.stop_log_find(token)
            self.write({'last_connection': fields.Datetime.now()})
            return True

        except Exception as e:
            _logger.error("Error fetching logs from NVR %s: %s", self.name, str(e))
            self.write({'state': 'error'})
            return False

    def action_view_logs(self):
        """Open the logs related to this NVR server"""
        self.ensure_one()
        return {
            'name': _('NVR Logs'),
            'type': 'ir.actions.act_window',
            'view_mode': 'list,form',
            'res_model': 'nvr.log',
            'domain': [('server_id', '=', self.id)],
            'context': {'default_server_id': self.id},
        }

    @api.model
    def _cron_fetch_logs(self):
        """Cron job to fetch logs from all active NVR servers"""
        servers = self.search([('active', '=', True), ('state', '=', 'tested')])
        for server in servers:
            try:
                server.fetch_logs()
            except Exception as e:
                _logger.error("Error in cron job fetching logs for server %s: %s", server.name, str(e))

class DahuaRpc:
    """Wrapper around the original DahuaRpc class"""
    
    def __init__(self, host, username, password, port=80):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session_id = None
        self.id = 10
        self.base_url = f"http://{host}:{port}"

    def request(self, method, params=None, url=None):
        self.id += 1
        data = {"method": method, "id": self.id}
        if params:
            data["params"] = params
        if self.session_id:
            data["session"] = self.session_id
        
        if not url:
            url = f"{self.base_url}/RPC2"
        
        response = self.session.post(url, json=data, verify=False)
        return response.json()

    def login(self):
        url = f"{self.base_url}/RPC2_Login"
        method = "global.login"
        
        # Step 1: Get session, realm, and random
        params = {
            "userName": self.username,
            "password": "",
            "clientType": "Web3.0"
        }
        
        try:
            response = self.request(method, params, url)
            self.session_id = response["session"]
            realm = response["params"]["realm"]
            random = response["params"]["random"]

            # Step 2: Encrypt password
            pwd_phrase = f"{self.username}:{realm}:{self.password}"
            pwd_hash = hashlib.md5(pwd_phrase.encode("utf-8")).hexdigest().upper()
            pass_phrase = f"{self.username}:{random}:{pwd_hash}"
            pass_hash = hashlib.md5(pass_phrase.encode("utf-8")).hexdigest().upper()

            # Step 3: Perform real login
            params = {
                "userName": self.username,
                "password": pass_hash,
                "clientType": "Web3.0",
                "authorityType": "Default",
                "passwordType": "Default"
            }
            response = self.request(method, params, url)
            return response.get("result", False)
            
        except Exception as e:
            _logger.error("Dahua login error: %s", str(e))
            return False

    def start_log_find(self, start_time, end_time, types, exclude_types=None):
        method = "log.startFind"
        params = {
            "condition": {
                "Types": types,
                "StartTime": start_time,
                "EndTime": end_time,
                "Translate": True,
                "Order": "Descent",
            }
        }
        if exclude_types:
            params["condition"]["ExcludeTypes"] = exclude_types
            
        response = self.request(method, params)
        if response.get("result"):
            return response["params"]["token"]
        else:
            raise Exception("Failed to start log find: " + str(response))

    def get_log_count(self, token):
        method = "log.getCount"
        params = {"token": token}
        response = self.request(method, params)
        if response.get("result"):
            return response["params"]["count"]
        else:
            raise Exception("Failed to get log count: " + str(response))

    def get_log_items(self, token, offset, count):
        method = "log.doSeekFind"
        params = {"token": token, "offset": offset, "count": count}
        response = self.request(method, params)
        if response.get("result"):
            return response["params"]["items"]
        else:
            raise Exception("Failed to get log items: " + str(response))

    def stop_log_find(self, token):
        method = "log.stopFind"
        params = {"token": token}
        response = self.request(method, params)
        return response["result"]
