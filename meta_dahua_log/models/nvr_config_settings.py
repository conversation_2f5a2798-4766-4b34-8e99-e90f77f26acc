from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class NVRLogType(models.Model):
    _name = 'nvr.log.type'
    _description = 'NVR Log Type Configuration'
    _order = 'name'

    name = fields.Char(string='Name', required=True, translate=True)
    code = fields.Char(string='Code', required=True, help="Internal code used by Dahua NVR")
    description = fields.Text(string='Description', translate=True)
    active = fields.Boolean(string='Active', default=True)
    notify_users = fields.Boolean(string='Enable Inbox Notifications', default=False,
                                help="Enable inbox notifications for this log type")
    user_ids = fields.Many2many('res.users', string='Users to Notify',
                              help="Users who will receive inbox notifications for this log type")
    log_count = fields.Integer(compute='_compute_log_count', string='Logs')

    _sql_constraints = [
        ('code_uniq', 'UNIQUE(code)', 'Log type code must be unique!'),
    ]

    def _compute_log_count(self):
        for log_type in self:
            log_type.log_count = self.env['nvr.log'].search_count([
                ('log_type', '=', log_type.code.split('.')[0]),
                ('log_subtype', '=', '.'.join(log_type.code.split('.')[1:]) if '.' in log_type.code else '')
            ])

    def action_view_logs(self):
        self.ensure_one()
        log_type = self.code.split('.')[0]
        log_subtype = '.'.join(self.code.split('.')[1:]) if '.' in self.code else ''
        
        domain = [('log_type', '=', log_type)]
        if log_subtype:
            domain.append(('log_subtype', '=', log_subtype))
            
        return {
            'name': _('Logs for %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'nvr.log',
            'view_mode': 'tree,form',
            'domain': domain,
            'context': {
                'default_log_type': log_type,
                'default_log_subtype': log_subtype,
            },
        }
