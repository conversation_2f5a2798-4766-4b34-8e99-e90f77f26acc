from odoo import api, fields, models
import odoo.addons.decimal_precision as dp
from odoo.exceptions import ValidationError

import logging
import pprint
_logger = logging.getLogger(__name__)


class Respartner(models.Model):
    _inherit = "res.partner"
    
    
    # def write(self, values):
    #     vals = values._get('customer_rank')
        
    #     _logger.info("customer_rank %s",
    #                     pprint.pformat(vals))
        
        # if vals > 0:
        #     customer_welcome_msg = self.env['ir.default'].sudo()._get('res.config.settings', 'customer_welcome_msg')
        #     if customer_welcome_msg:
        #         if self.phone:
        #             mobile = self.phone.replace('-', '').replace(' ', '')
        #             sms_text = self.env['ir.default'].sudo()._get('res.config.settings', 'customer_welcome_content').replace('<name>', self.name)
                    
        #             self.env['send.sms'].send_sms(mobile, sms_text)
        #         else:
        #             raise ValidationError("Please enter phone number!")
        # result = super(Respartner, self).write(values)
        # return result

    @api.model_create_multi
    def create(self, val_list):
        partners = super(Respartner, self).create(val_list)
        for res in partners:        
            if res.customer_rank > 0:
                customer_welcome_msg = self.env['ir.default'].sudo()._get('res.config.settings', 'customer_welcome_msg')
                if customer_welcome_msg:
                    if res.phone:
                        mobile = res.phone.replace('-', '').replace(' ', '')
                        sms_text = self.env['ir.default'].sudo()._get('res.config.settings', 'customer_welcome_content').replace('<name>', res.name)
                        
                        self.env['send.sms'].send_sms(mobile, sms_text)
                    else:
                        raise ValidationError("Please enter phone number!")
        return partners
    
    

    