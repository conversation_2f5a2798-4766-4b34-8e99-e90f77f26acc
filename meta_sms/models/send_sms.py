# -*- coding: utf-8 -*-
import requests
import base64
import json
import string
import random
import logging
import pprint
from odoo import api, models, _
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class SendSMS(models.TransientModel):
    _name = "send.sms"

    # @api.multi
    def send_sms(self, number, sms_text):

        # raise ValidationError("number: {} and text: {}".format(number, sms_text))
        partner_id = self.env['res.partner'].sudo().search(['&','|',('phone','=',number),('mobile','=',number),('country_id','!=',False)],limit=1)

        sms_provider = self.env["ir.default"].sudo()._get("res.config.settings", "sms_provider")
        # sms_text = self.env['ir.default'].sudo()._get('website.otp.settings', 'otp_content').replace('<otp>', otp)

        sender_id = ""
        api_response = ""

        if sms_provider == "elitbuzz":
            api_key = (
                self.env["ir.default"].sudo()._get("res.config.settings", "elitbuzz_api_key")
            )
            sender_id = senderid = (
                self.env["ir.default"].sudo()._get("res.config.settings", "elitbuzz_senderid")
            )

            payload = {
                "contacts": number,
                "msg": sms_text,
                "api_key": api_key,
                "type": "text",
                "senderid": senderid,
            }
            header = {
                # "HTTP_CF_CONNECTING_IP" : '*************'
            }
            
            
            #Original 
            # r = requests.post('https://sms.metamorphosis.com.bd/smsapi?', params=payload, headers=header)

            # After disaster happedend on Elitbuzz
            try:
                r = requests.post('https://880sms.com/smsapi?', params=payload, headers=header, timeout=(10, 10))
            except requests.exceptions.Timeout:
                logging.error(f"Connection Timeout on Elitbuzz SMS Server")
                
            except requests.exceptions.RequestException as e:
                logging.error(f"An error occurred Elitbuzz SMS Server: {e}")
            api_response = pprint.pformat(r.content)

            _logger.info("Send sms with elitbuzz %s", pprint.pformat(r.content))

            # raise ValidationError(r.content)
        elif sms_provider == "isms":
            api_token = (
                self.env["ir.default"].sudo()._get("res.config.settings", "isms_api_token")
            )
            sender_id = sid = (
                self.env["ir.default"].sudo()._get("res.config.settings", "isms_sid")
            )
            csms_id = random.randint(00000000,99999999)

            payload = {
                "msisdn": number,
                "sms": sms_text,
                "api_token": api_token,
                "type": "text",
                "sid": sid,
                "csms_id": csms_id,
            }
            try:
                r = requests.post(
                    "https://smsplus.sslwireless.com/api/v3/send-sms", params=payload, timeout=(10, 10)
                )
            except requests.exceptions.Timeout:
                logging.error(f"Connection Timeout on SSLWireless SMS Server")
                
            except requests.exceptions.RequestException as e:
                logging.error(f"An error occurred SSLWireless SMS Server: {e}")

            api_response = pprint.pformat(r.content)

            _logger.info("Send sms with iSMS %s", pprint.pformat(r.content))

            # raise ValidationError(r.content)

        # elif sms_provider == 'infobip':
        # Send Infobip OTP here
        # infobip_url = 'https://api.infobip.com/2fa/1/pin'
        # username = 'Baatighar'
        # password = 'q<u+=KS&q,N6gS5!'

        # credentials = (username + ':' + password).encode('utf-8')
        # base64_encoded_credentials = base64.b64encode(credentials).decode('utf-8')

        # headers = {
        #     'Authorization': 'Basic ' + base64_encoded_credentials
        # }

        # payload = dict()
        # payload.update({
        #     'applicationId': 'CADC62B3A99E2D8E90326171E7192485',
        #     'messageId': '977ACC30CDFCB27F62BE737A834EC432',
        #     'from': '8804445654336',
        #     'to': '+8801796143408'
        # })

        # response = requests.post(infobip_url, data=payload, headers=headers)

        elif sms_provider == "gp":
                gp_username = (
                    self.env["ir.default"]
                    .sudo()
                    ._get("res.config.settings", "gp_username")
                )
                gp_password = (
                    self.env["ir.default"]
                    .sudo()
                    ._get("res.config.settings", "gp_password")
                )

                gp_cli_masking = (
                    self.env["ir.default"]
                    .sudo()
                    ._get("res.config.settings", "gp_cli_masking")
                )

                gp_bill_msisdn = (
                    self.env["ir.default"]
                    .sudo()
                    ._get("res.config.settings", "gp_bill_msisdn")
                )

                clienttransid = "".join(
                    random.choice(string.ascii_lowercase + string.digits)
                    for _ in range(25)
                )

                country_code = (
                    str(partner_id.country_id.phone_code)
                    if partner_id and partner_id.country_id
                    else "880"
                )

                payload = json.dumps(
                    {
                        "username": gp_username,
                        "password": gp_password,
                        "apicode": "1",
                        "msisdn": [number],
                        "countrycode": country_code,
                        "cli": gp_cli_masking,
                        "messagetype": "1",
                        "message": sms_text,
                        "clienttransid": clienttransid,
                        "bill_msisdn": gp_bill_msisdn,
                        "tran_type": "T",
                        "request_type": "S",
                        "rn_code": "71",
                    }
                )
                
                try:
                
                    gp_response = requests.post(
                        "https://gpcmp.grameenphone.com/gp/ecmapigw/webresources/ecmapigw.v3",
                        headers={"Content-Type": "application/json"},
                        data=payload, timeout=(10, 10)
                    )
                except requests.exceptions.Timeout:
                    logging.error(f"Connection Timeout on GrameenPhone SMS Server")
                except requests.exceptions.RequestException as e:
                    logging.error(f"An error occurred GrameenPhone SMS Server: {e}")
                    
                logging.warning(
                    "grameen phone sms response status_code {} : response text {}".format(
                        gp_response.status_code, gp_response.text
                    )
                )

                api_response = pprint.pformat(gp_response.text)
                sender_id = gp_bill_msisdn

        sms_log_model = self.env["sms.logs"].sudo()
        sms_log_model.create(
            {
                "api_response": api_response,
                "sender_id": sender_id,
                "to_number": number,
                "sms_body": sms_text,
                "send_sms": True,
                "sms_provider": sms_provider,
            }
        )

        return True
