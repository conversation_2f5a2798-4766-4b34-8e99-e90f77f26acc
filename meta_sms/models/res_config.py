from odoo import api, fields, models, _


class MetaSMSSettings(models.TransientModel):
    _inherit = "res.config.settings"
    # _name = 'sms.settings'
    _description = "res_config_settings"

    sms_provider = fields.Selection(
        [("elitbuzz", "Elitbuzz"), ("isms", "iSMS"), ("gp", "Grameen Phone")],
        string="SMS Provider",
    )

    elitbuzz_api_key = fields.Char("Elitbuzz API Key")
    elitbuzz_senderid = fields.Char("Elitbuzz Sender ID")

    isms_api_token = fields.Char("iSMS API Token")
    isms_sid = fields.Char("iSMS Sender ID")

    gp_username = fields.Char(
        "GP Username", help="""Valid Username given by the administrator"""
    )
    gp_password = fields.Char(
        "GP Password",
        help="""Valid Password given by the administrator/It needs to be
            reset from web portal for the first time. Web URL link:
            https://gpcmp.grameenphone.com/#/changepassword""",
    )
    gp_cli_masking = fields.Char(
        "GP CLI/Masking",
        help="""CLI means Masking. So the masking created for your account
                Ex: GP ICT""",
    )
    gp_bill_msisdn = fields.Char(
        "GP Billing MSISDN",
        help="""The CMP billing MSISDN/CMP account
                MSISDN. number prefix will start with ZERO""",
    )
    

    customer_welcome_msg = fields.Boolean(string="Customer Welcome SMS")
    customer_welcome_content = fields.Text("Customer Welcome Content")

    order_confirmation_msg = fields.Boolean(string="Order Confirmation SMS")
    order_confirmation_content = fields.Text("Order Confirmation Content")

    order_shipped_msg = fields.Boolean(string="Order Shipped SMS")
    order_shipped_content = fields.Text("Order Shipped Content")

    order_delivered_msg = fields.Boolean(string="Order Delivered SMS")
    order_delivered_content = fields.Text("Order Delivered Content")

    # @api.multi
    def set_values(self):
        super(MetaSMSSettings, self).set_values()
        IrDefault = self.env["ir.default"].sudo()
        IrDefault.set("res.config.settings", "sms_provider", self.sms_provider)
        IrDefault.set("res.config.settings", "elitbuzz_api_key", self.elitbuzz_api_key)
        IrDefault.set(
            "res.config.settings", "elitbuzz_senderid", self.elitbuzz_senderid
        )
        IrDefault.set("res.config.settings", "isms_api_token", self.isms_api_token)
        IrDefault.set("res.config.settings", "isms_sid", self.isms_sid)
        IrDefault.set(
            "res.config.settings", "customer_welcome_msg", self.customer_welcome_msg
        )
        IrDefault.set(
            "res.config.settings",
            "customer_welcome_content",
            self.customer_welcome_content,
        )
        IrDefault.set(
            "res.config.settings", "order_confirmation_msg", self.order_confirmation_msg
        )
        IrDefault.set(
            "res.config.settings",
            "order_confirmation_content",
            self.order_confirmation_content,
        )
        IrDefault.set(
            "res.config.settings", "order_shipped_msg", self.order_shipped_msg
        )
        IrDefault.set(
            "res.config.settings", "order_shipped_content", self.order_shipped_content
        )
        IrDefault.set(
            "res.config.settings", "order_delivered_msg", self.order_delivered_msg
        )
        IrDefault.set(
            "res.config.settings",
            "order_delivered_content",
            self.order_delivered_content,
        )

        # FOR GRAMEEN PHONE

        IrDefault.set("res.config.settings", "gp_username", self.gp_username)
        IrDefault.set("res.config.settings", "gp_password", self.gp_password)
        IrDefault.set("res.config.settings", "gp_cli_masking", self.gp_cli_masking)
        IrDefault.set("res.config.settings", "gp_bill_msisdn", self.gp_bill_msisdn)
        return True

    # @api.multi
    def get_values(self):
        res = super(MetaSMSSettings, self).get_values()
        IrDefault = self.env["ir.default"].sudo()
        res.update(
            {
                "sms_provider": IrDefault._get(
                    "res.config.settings", "sms_provider", self.sms_provider
                ),
                "elitbuzz_api_key": IrDefault._get(
                    "res.config.settings", "elitbuzz_api_key", self.elitbuzz_api_key
                ),
                "elitbuzz_senderid": IrDefault._get(
                    "res.config.settings", "elitbuzz_senderid", self.elitbuzz_senderid
                ),
                "isms_api_token": IrDefault._get(
                    "res.config.settings", "isms_api_token", self.isms_api_token
                ),
                "isms_sid": IrDefault._get(
                    "res.config.settings", "isms_sid", self.isms_sid
                ),
                "customer_welcome_msg": IrDefault._get(
                    "res.config.settings",
                    "customer_welcome_msg",
                    self.customer_welcome_msg,
                ),
                "customer_welcome_content": IrDefault._get(
                    "res.config.settings",
                    "customer_welcome_content",
                    self.customer_welcome_content,
                ),
                "order_confirmation_msg": IrDefault._get(
                    "res.config.settings",
                    "order_confirmation_msg",
                    self.order_confirmation_msg,
                ),
                "order_confirmation_content": IrDefault._get(
                    "res.config.settings",
                    "order_confirmation_content",
                    self.order_confirmation_content,
                ),
                "order_shipped_msg": IrDefault._get(
                    "res.config.settings", "order_shipped_msg", self.order_shipped_msg
                ),
                "order_shipped_content": IrDefault._get(
                    "res.config.settings",
                    "order_shipped_content",
                    self.order_shipped_content,
                ),
                "order_delivered_msg": IrDefault._get(
                    "res.config.settings",
                    "order_delivered_msg",
                    self.order_delivered_msg,
                ),
                "order_delivered_content": IrDefault._get(
                    "res.config.settings",
                    "order_delivered_content",
                    self.order_delivered_content,
                ),

                # FOR GRAMEEN PHONE
                "gp_username": IrDefault._get(
                    "res.config.settings", "gp_username", self.gp_username
                ),
                "gp_password": IrDefault._get(
                    "res.config.settings", "gp_password", self.gp_password
                ),
                "gp_cli_masking": IrDefault._get(
                    "res.config.settings", "gp_cli_masking", self.gp_cli_masking
                ),
                "gp_bill_msisdn": IrDefault._get(
                    "res.config.settings", "gp_bill_msisdn", self.gp_bill_msisdn
                ),
            }
        )
        return res
    