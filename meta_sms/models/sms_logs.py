# -*- coding: utf-8 -*-
from odoo import fields, api, models, _
import requests
import base64
import json
from odoo.exceptions import ValidationError
import logging
import pprint

_logger = logging.getLogger(__name__)
from odoo.tools.float_utils import float_compare, float_repr, float_round


class MetaSMSLogs(models.Model):
    _name = "sms.logs"
    _description = "SMS Logs"

    customer_id = fields.Many2one("res.partner", string="Customer")
    api_response = fields.Char("API response")
    sender_id = fields.Char("Sender ID")
    to_number = fields.Char("Customer Number")
    sms_provider = fields.Char("Provider", required=True)
    sms_body = fields.Char("Body", required=True)
    send_sms = fields.Boolean(string="Send SMS", default=False)
