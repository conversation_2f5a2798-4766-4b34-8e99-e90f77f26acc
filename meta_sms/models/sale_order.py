from odoo import api, fields, models
from odoo.exceptions import ValidationError

import logging
_logger = logging.getLogger(__name__)


# from odoo.addons.meta_paperfly_order.models.sale_order_paperfly import MetaPaperflyOrder

class SaleOrder(models.Model):
    _inherit = "sale.order"
    customer_mobile = fields.Char('Mobile', related='partner_id.phone')
    courier_delivery_status = fields.Selection([('pending', 'Pending'), ('shipped', 'Shipped'), ('delivered', 'Delivered'), ('return', 'Return')], string="Delivery Status", default="pending")
    courier_delivery_comment = fields.Text(string="Comment")
    
    
    def write(self, values):
        courier_delivery_status = values.get('courier_delivery_status')
        so_state = values.get('state')
        result = super(SaleOrder, self).write(values)
        
        for rec in self:
            try:
                mobile = rec.partner_id.mobile or rec.partner_id.phone or False
                if mobile:
                    if so_state == 'sent':
                        order_confirmation_msg = self.env['ir.default'].sudo()._get('res.config.settings', 'order_confirmation_msg')
                        # raise ValidationError(res.partner_id.mobile.replace('-', '').replace(' ', ''))
                        if order_confirmation_msg:
                            mobile = mobile.replace('-', '').replace(' ', '')
                            sms_text = self.env['ir.default'].sudo()._get('res.config.settings', 'order_confirmation_content').replace('<name>', rec.partner_id.name).replace('<order_id>', rec.name)
                            
                            self.env['send.sms'].send_sms(mobile, sms_text)
                    
                    if courier_delivery_status == 'delivered':
                        order_delivered_msg = self.env['ir.default'].sudo()._get('res.config.settings', 'order_delivered_msg')
                        if order_delivered_msg:
                            mobile = mobile.replace('-', '').replace(' ', '')
                            sms_text = self.env['ir.default'].sudo()._get('res.config.settings', 'order_delivered_content').replace('<name>', rec.partner_id.name).replace('<order_id>', rec.name)
                            
                            self.env['send.sms'].send_sms(mobile, sms_text)
                            # else:
                            #     raise ValidationError("Please enter phone number!")
                    if courier_delivery_status == 'shipped':
                        order_shipped_msg = self.env['ir.default'].sudo()._get('res.config.settings', 'order_shipped_msg')
                        if order_shipped_msg:
                                mobile = mobile.replace('-', '').replace(' ', '')
                                sms_text = self.env['ir.default'].sudo()._get('res.config.settings', 'order_shipped_content').replace('<name>', rec.partner_id.name).replace('<order_id>', rec.name)
                                self.env['send.sms'].send_sms(mobile, sms_text)
                                
            except Exception as e:
                _logger.error(f"Error While sending SMS of {rec.name} {so_state} -> {courier_delivery_status} -> {e}" )
                pass
                
        return result
    
    # @api.model
    # def create(self, vals):
    #     res = super(SaleOrder, self).create(vals)
        
    #     order_confirmation_msg = self.env['ir.default'].sudo()._get('res.config.settings', 'order_confirmation_msg')
    #     # raise ValidationError(res.partner_id.mobile.replace('-', '').replace(' ', ''))
    #     if order_confirmation_msg:
    #         if res.partner_id.phone:
    #             mobile = res.partner_id.phone.replace('-', '').replace(' ', '')
    #             sms_text = self.env['ir.default'].sudo()._get('res.config.settings', 'order_confirmation_content').replace('<name>', res.partner_id.name).replace('<order_id>', res.name)
                
    #             self.env['send.sms'].send_sms(mobile, sms_text)
    #         # else:
    #         #     raise ValidationError("Please enter phone number!")
    #     return res

    def action_c_shipped(self, field_names=None, arg=False):
        # MetaPaperflyOrder.action_c_shipped_force(self)
        self.write({
                'courier_delivery_status': 'shipped'
            })
    def action_c_delivered(self, field_names=None, arg=False):
        self.write({
                'courier_delivery_status': 'delivered'
            })
    def action_c_return(self, field_names=None, arg=False):
        self.write({
                'courier_delivery_status': 'return'
            })
    def action_order_confirmation_msg(self, field_names=None, arg=False):
        order_confirmation_msg = self.env['ir.default'].sudo()._get('res.config.settings', 'order_confirmation_msg')
        if order_confirmation_msg:
            for rec in self:
                mobile = rec.partner_id.mobile or rec.partner_id.phone or False
                if mobile:
                    mobile = mobile.replace('-', '').replace(' ', '')
                    sms_text = self.env['ir.default'].sudo()._get('res.config.settings', 'order_confirmation_content').replace('<name>', rec.partner_id.name).replace('<order_id>', rec.name)
                    
                    self.env['send.sms'].send_sms(mobile, sms_text)
                else:
                    raise ValidationError("Please enter phone number!")
