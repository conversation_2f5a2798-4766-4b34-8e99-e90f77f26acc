# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

# will intigrate in future

import logging
import threading
import ast
from uuid import uuid4
from werkzeug.urls import url_join

from odoo.exceptions import UserError
from odoo import api, fields, models, tools, _, Command


_logger = logging.getLogger(__name__)


class SmsSms(models.Model):
    _inherit = "sms.sms"
    

    failure_type = fields.Selection(
        selection_add=[
            ("account_not_activated", "Account has not been activated"),
            ("invalid_number", "Number is inactive or invalid"),
            ("network_error", "Network or routing error"),
            ("rejected", "Rejected by the recipient"),
        ]
    )

    def send(
        self,
        unlink_failed=False,
        unlink_sent=True,
        auto_commit=False,
        raise_exception=False,
    ):
        """Main API method to send SMS.
        Overridden by ClickSend Integration

        :param unlink_failed: unlink failed SMS after IAP feedback;
        :param unlink_sent: unlink sent SMS after IAP feedback;
        :param auto_commit: commit after each batch of SMS;
        :param raise_exception: raise if there is an issue contacting IAP;
        """
        self = self.filtered(lambda sms: sms.state == "outgoing" and not sms.to_delete)
        for batch_ids in self._split_batch():
            self.browse(batch_ids)._send_via_clicksend(
                unlink_failed=unlink_failed,
                unlink_sent=unlink_sent,
                raise_exception=raise_exception,
            )
            # auto-commit if asked except in testing mode
            if auto_commit is True and not getattr(
                threading.current_thread(), "testing", False
            ):
                self._cr.commit()

    def _send_via_clicksend(
        self, unlink_failed=False, unlink_sent=True, raise_exception=False
    ):
        """Send SMS Via ClickSend after checking the number (presence and formatting)."""

        if (
            not self.env.company.clicksend_username
            or not self.env.company.clicksend_password
        ):
            raise UserError(
                f"Odoo company {self.env.company.name} does not have Clicksend Credentials set to it. Please Input the Credentials in Company."
            )

        messages = [
            {
                "body": sms.body,
                "to": sms.number,
                "from": self.env.company.clicksend_sender_id or None,
                "custom_string": sms.uuid,
            }
            for sms in self
        ]
        try:
            results = ClicksendSmsApi(self.env)._send_sms_batch(messages)
        except Exception as e:
            _logger.error(
                "Sent batch %s SMS: %s: failed with exception %s",
                len(self.ids),
                self.ids,
                e,
            )
            if raise_exception:
                raise
        if isinstance(results, str):
            try:
                results = ast.literal_eval(results)
                _logger.info(f"isinstance(results, str) {results}")
            except Exception as e:
                _logger.error(
                    f"Error while converting ClickSend SMS responce to Dictionary: {e}"
                )
                if raise_exception:
                    raise

        if results["http_code"] not in [200, 201]:
            if raise_exception:
                raise

        if "data" in results and "messages" in results["data"]:
            messages = results["data"]["messages"]
            if any(message["status"] == "INSUFFICIENT_CREDIT" for message in messages):
                self._action_clicksend_insufficient_credit()

            results_uuids = [message["custom_string"] for message in messages]
            all_sms_sudo = (
                self.env["sms.sms"]
                .sudo()
                .search([("uuid", "in", results_uuids)])
                .with_context(sms_skip_msg_notification=True)
            )

            for message in messages:
                sms_sudo = all_sms_sudo.filtered(
                    lambda s: s.uuid == message["custom_string"]
                )
                if message["status"] == "SUCCESS":
                    # sms_sudo.sms_tracker_id._action_update_from_sms_state("sent")
                    sms_sudo.write(
                        {
                            "state": "pending",
                            "failure_type": False,
                            "to_delete": unlink_sent,
                        }
                    )

                elif message["status"] not in ["INSUFFICIENT_CREDIT"]:
                    failure_type = CLICKSEND_API_RESPONSES.get(
                        message["status"], "unknown"
                    )
                    if failure_type != "unknown":
                        sms_sudo.sms_tracker_id._action_update_from_sms_state(
                            "error", failure_type=failure_type
                        )
                    sms_sudo.write(
                        {
                            "state": "error",
                            "failure_type": failure_type,
                            "to_delete": unlink_failed,
                        }
                    )

            all_sms_sudo.mail_message_id._notify_message_notification_update()

    def _action_clicksend_insufficient_credit(self):
        mail_composer = self.env["mail.compose.message"].sudo()
        res_user = self.env.company.clicksend_responsible_id
        if res_user:
            body = f"""
                <p>
                    Dear {res_user.name}
                    Our current balance for the ClickSend SMS Gateway has been depleted. 
                    <br/><br/>
                    Please follow the <a href="https://dashboard.clicksend.com/account/billing-recharge/top-up-account">Link to Top-Up Account</a>. 
                    <br/></br>
                    Also please Mark the To Do as done as well. 
                </p>
            """

            mail_composer.create(
                {
                    "subject": "Urgent: ClickSend SMS Gateway Topup Needed",
                    "composition_mode": "comment",
                    "partner_ids": [Command.link(res_user.partner_id.id)],
                    "model": res_user.partner_id._name,
                    "res_ids":res_user.partner_id.ids,
                    "body": body,
                    "force_send": True,
                    "email_layout_xmlid": "mail.mail_notification_light",
                }
            ).action_send_mail()
        
