<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">
            Two layer Authentication
        </h2>
        
        <div class="row py-1 mx-1" style="font-weight:500; font-size:18px; color:#484848; font-family:'Montserrat', sans-serif">
            <div class="container col-md-6" style="font-weight:500;line-height: 24px;font-size:16px;color:#484848;font-family:'Montserrat', sans-serif; vertical-align: middle; display: grid;">
                <p class="mb4 mt4 mr8">
                    Two layer Authentication:An impressive application which adds extra layer of security and provides an option to log-in/sign-up via OTP.
                </p>
            </div>
            
        </div>
    
    </div>
</section>

<section class="oe_container oe_dark lead">

    <div class="oe_row">
        <div class="panel panel-primary" id="wkid0">
        <div class="panel-heading">
        <h3 class="panel-title" style="font-size: 25px;"><i class="fa fa-mail-forward"></i> Features:</h3>
        </div>
            <div class="panel-body">
                <ul class="list-unstyled">
                    <li><i class="fa fa-check text-primary"></i> Functionality to enable OTP during sign-up</li>
                    <li><i class="fa fa-check text-primary"></i> Functionality to enable OTP during sign-in</li>
                    <li><i class="fa fa-check text-primary"></i> Flexibility to login via OTP or password.</li>
                    <li><i class="fa fa-check text-primary"></i> Validates valid email during sign-up</li>
                    <li><i class="fa fa-check text-primary"></i> The OTP validation information along with their success status will be maintained in the backend.</li>
                    <li><i class="fa fa-check text-primary"></i> Check existing user during sign-up</li>
                    <li><i class="fa fa-check text-primary"></i> An OTP is sent to a registered email address and the user will only be able to register/login on entering the same OTP.</li>
                    <li><i class="fa fa-check text-primary"></i> Admin can set expiry time and email template of OTP.</li>
                    <li><i class="fa fa-check text-primary"></i> Admin can set OTP type (password or text).</li>
                    <li><i class="fa fa-check text-primary"></i> Cross browser compatible.</li>
                    
                </ul>
            </div>
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <div class="oe_span12">
        
        </div>
    </div>
</section>


<section class="oe_container">
    <ul class="nav nav-tabs d-flex justify-content-center ">
        <li class="active">
            <a href="#website_otp_auth_tab1" >
                <h3 class="panel-title"><i class="fa fa-sign-in"></i> <b>Sign-in View</b> </h3>
            </a>
        </li>
        <li>
            <a href="#website_otp_auth_tab2" >
                <h3 class="panel-title"><i class="fa fa-user-plus"></i> <b>Sign-up View</b> </h3>
            </a>
        </li>
      
    </ul>
</section>
<section id="website_otp_auth_tab1">
    <section class="oe_container">
        <div class="oe_row oe_spaced">
            <h2 class="oe_slogan" style="color:#875A7B;">Flexibility on setting</h2>
            <h3 class="oe_slogan">User can enable/disable sign-in setting
            </h3>
          
        </div>
        <div class="oe_row oe_spaced">
            <h2 class="oe_slogan" style="color:#875A7B;">Elegant Login Interface</h2>
            <div class="oe_span12">
     
            </div>
        </div>
        <div class="oe_row oe_spaced">
            <h2 class="oe_slogan" style="color:#875A7B;">Login Options</h2>
            <h3 class="oe_slogan">User can login via OTP or password
            </h3>
            
        </div>
        <div class="oe_row oe_spaced">
            <h2 class="oe_slogan" style="color:#875A7B;">OTP Expiry</h2>
            <h3 class="oe_slogan">Live otp expiry will be visible to the user
            </h3>
            <div class="oe_span12">
               
            </div>
        </div>
        <div class="oe_row oe_spaced">
            <h2 class="oe_slogan" style="color:#875A7B;">Non register User</h2>
            <h3 class="oe_slogan">During log-in module gives error status for non register users
            </h3>
            <div class="oe_span12">
                
            </div>
        </div>
    </section>
</section>
<section id="website_otp_auth_tab2">
    <section class="oe_container">
        <div class="oe_row oe_spaced">
            <h2 class="oe_slogan" style="color:#875A7B;">Elegant Sign-up Interface</h2>
            <div class="oe_span12">
                
            </div>
        </div>
        <div class="oe_row oe_spaced">
            <h2 class="oe_slogan" style="color:#875A7B;">OTP Expiry</h2>
            <h3 class="oe_slogan">Live otp expiry will be visible to the user
            </h3>
            <div class="oe_span12">
               
            </div>
            <div class="oe_span12">
                
            </div>
        </div>
        <div class="oe_row oe_spaced">
            <h2 class="oe_slogan" style="color:#875A7B;">Resend OTP</h2>
            <h3 class="oe_slogan">If OTP is expired then users flexibility to resend OTP
            </h3>
            <div class="oe_span12">
                
            </div>
        </div>
        <div class="oe_row oe_spaced">
            <h2 class="oe_slogan" style="color:#875A7B;">E-mail Validation/Existence</h2>
            <h3 class="oe_slogan">Module helps to avoid user to register with invalid/existing e-mail address
            </h3>
            <div class="oe_span12">
                
            </div>
            <div class="oe_span12">
                
            </div>
        </div>
    </section>
</section>


<!-- <section class="pt32" id="webkul_support">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center">Help and Support</h2>
            <p class="mb-2 text-center" style="font-size:16px; color:#333; font-weight:400">Get Immediate support for any of your query</p>
        </div>
        <div class="col-12">
            <p class="text-center px-5" style="font-size:14px; color:#555; font-weight:normal">You will get 90 days free support for any doubt, queries, and bug fixing (excluding data recovery) or any type of issue related to this module.</p>
        </div>
        <div class="mx-auto col-lg-9 mb-4 oe_screenshot">
            <div class="row align-items-center justify-content-center mx-0 p-3">
                <div class="col-sm-2 text-center pr-0">
                    <img src="mail.png" alt="mail" class="img img-fluid">
                </div>
                <div class="col-xl-7 col-sm-10">
                    <p class="my-2" style="color:#555; font-weight:bold">Write a mail to us:</p>
                    <b class="text-dark" style="font-size:18px"><EMAIL></b>
                    <p class="my-2" style="font-size:14px; color:#777; font-weight:normal">Any queries or want any extra features? Just drop a mail to our support.</p>
                </div>
                <div class="col-xl-3 offset-xl-0 float-xl-right col-sm-10 offset-sm-2 float-left mt16">
                    <a href="mailto:<EMAIL>" style="padding:10px 22px; background-color:#2335D7; font-size:14px; color:#fff"><i class="fa fa-pencil-square-o" style="color:white; margin-right:4px"></i>Write To US</a>
                </div>
            </div>
        </div>
        <div class="mx-auto col-lg-9 oe_screenshot">
            <div class="row align-items-center justify-content-center mx-0 p-3">
                <div class="col-sm-2 text-center pr-0">
                    <img src="support-icon.png" alt="support-icon" class="img img-fluid">
                </div>
                <div class="col-sm-10 ">
                    <p class="my-2" style="font-weight:bold; color:#555">Get in touch with our Expert:</p>
                    <b class="text-dark text-break" style="font-size:18px">https://webkul.uvdesk.com/en/customer/create-ticket/</b>
                    <p class="my-2" style="font-weight:normal; font-size:14px; color:#777">Have any technical queries, want extra features, or anything else? Our team is here to answer all your questions. Just Raise A Support Ticket.</p>
                </div>
            </div>
        </div>
    </div> -->
</section>



<!-- <section class="oe_container">
<div class="oe_span12" style="max-width: 100%;">
    <img src="http://odooimg.webkul.com/analytics/piwik/piwik.php?idsite=3&rec=1&action_name=otp_auth&url=https://apps.openerp.com/apps/modules/13.0/otp_auth&uid=otp_auth" style="border:0" alt="" />
</div>
</section> -->
