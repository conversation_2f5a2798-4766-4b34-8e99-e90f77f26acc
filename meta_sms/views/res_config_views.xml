<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="sms_conf_view" model="ir.ui.view">
            <field name="name">meta.sms.setting.form</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="base.res_config_settings_view_form" />
            <field name="arch" type="xml">

                <block name="integration" position="after">
                    <block title="MetaSMS Configuration" name="meta_sms_configuration_block"
                        help="Controlls Global SMS provider and sms Triggers &amp; flow ">
                        <setting id="meta_sms_configuration_sms_provider" title="SMS Provider">
                            <div class="row mt8" >
                                <label class="col-lg-6" for="sms_provider"/>
                                <field name="sms_provider" />
                            </div>
                            <div class="row mt8" invisible="sms_provider!='elitbuzz'">
                                <label class="col-lg-6" for="elitbuzz_api_key"/>
                                <field name="elitbuzz_api_key" required="sms_provider =='elitbuzz'"/>
                            </div>
    
                            <div class="row mt8" invisible="sms_provider!='elitbuzz'">
                                <label class="col-lg-6" for="elitbuzz_senderid"/>
                                <field name="elitbuzz_senderid" required="sms_provider =='elitbuzz'"/>
                            </div>
    
                            <div class="row mt8" invisible="sms_provider!='isms'">
                                <label class="col-lg-6" for="isms_api_token"/>
                                <field name="isms_api_token" required="sms_provider =='isms'"/>
                            </div>
    
                            <div class="row mt8" invisible="sms_provider!='isms'">
                                <label class="col-lg-6" for="isms_sid"/>
                                <field name="isms_sid" required="sms_provider =='isms'"/>
                            </div>

                            <!-- GRAMEEN PHONE -->

                            <div class="row mt8" invisible="sms_provider!='gp'">
                                <label class="col-lg-6" for="gp_username"/>
                                <field name="gp_username" required="sms_provider =='gp'"/>
                            </div>
    
                            <div class="row mt8" invisible="sms_provider!='gp'">
                                <label class="col-lg-6" for="gp_password"/>
                                <field name="gp_password" required="sms_provider =='gp'"/>
                            </div>

                            <div class="row mt8" invisible="sms_provider!='gp'">
                                <label class="col-lg-6" for="gp_cli_masking"/>
                                <field name="gp_cli_masking" required="sms_provider =='gp'"/>
                            </div>
    
                            <div class="row mt8" invisible="sms_provider!='gp'">
                                <label class="col-lg-6" for="gp_bill_msisdn"/>
                                <field name="gp_bill_msisdn" required="sms_provider =='gp'"/>
                            </div>

                        </setting>
                        <setting id="meta_sms_configuration_customer_welcome"
                            title="Cutomer Signup SMS">

                            <field name="customer_welcome_msg" />
                            <field name="customer_welcome_content"
                                placeholder="e.g. Welcome to our company!"
                                invisible="not customer_welcome_msg" />
                        </setting>
                        <setting id="meta_sms_configuration_order_confirmation"
                            title="Order Confirm SMS">
                            <field name="order_confirmation_msg" />
                            <field name="order_confirmation_content"
                                invisible="not order_confirmation_msg"
                                placeholder="e.g. Dear customer, your order has been confirmed!" />
                        </setting>
                        <setting id="meta_sms_configuration_order_shipped"
                            title="Order Shipment SMS">
                            <field name="order_shipped_msg" />
                            <field name="order_shipped_content" invisible="not order_shipped_msg"
                                placeholder="e.g. Dear customer, your order has been shipped!" />
                        </setting>
                        <setting id="meta_sms_configuration_order_delivered"
                            title="Order Delivered SMS">
                            <field name="order_delivered_msg" />
                            <field name="order_delivered_content"
                                invisible="not order_delivered_msg"
                                placeholder="e.g. Dear customer, your order has been delivered!" />
                        </setting>
                    </block>
                </block>
                <!-- <xpath expr="//block[@name='integration']" position="after">
                    <div class="app_settings_block" data-string="MetaSMS Configuration"
                        string="MetaSMS Configuration" data-key="meta_sms">
                        <h2>MetaSMS Configuration</h2>
                        <div class="row mt10 o_settings_container">
                            <div class="col-xs-12 col-md-12 o_setting_box">
                                <sheet>
                                    <group>
                                        <field name="sms_provider" />
                                    </group> -->

                <!-- elitbuzz field -->
                <!-- <group name="elitbuzz_group"
                                        invisible="sms_provider != 'elitbuzz'"
                                        required="sms_provider =='elitbuzz'">
                                        <field name="elitbuzz_api_key" />
                                    </group>
                                    <group name="elitbuzz_group"
                                        invisible="sms_provider != 'elitbuzz'"
                                        required="sms_provider =='elitbuzz'">
                                        <field name="elitbuzz_senderid" />
                                    </group> -->

                <!-- isms field -->
                <!-- <group name="isms_group" invisible="sms_provider != 'isms'"
                                        required="sms_provider =='isms'">
                                        <field name="isms_api_token" />
                                    </group>
                                    <group name="isms_group" invisible="sms_provider != 'isms'"
                                        required="sms_provider =='isms'">
                                        <field name="isms_sid" />
                                    </group> -->

                <!-- Customer Welcome SMS -->
                <!-- <group>
                                        <field name="customer_welcome_msg" />
                                    </group>
                                    <group name="customer_welcome_group"
                                        invisible="not customer_welcome_msg">
                                        <field name="customer_welcome_content"
                                            placeholder="e.g. Welcome to our company!" />
                                    </group> -->

                <!-- Order Confirmation Message -->
                <!-- <group>
                                        <field name="order_confirmation_msg" />
                                    </group>
                                    <group name="order_confirmation_group"
                                        invisible="not order_confirmation_msg">
                                        <field name="order_confirmation_content"
                                            placeholder="e.g. Dear customer, your order has been confirmed!" />
                                    </group> -->

                <!-- Order Shipped Message -->
                <!-- <group>
                                        <field name="order_shipped_msg" />
                                    </group>
                                    <group name="order_shipped_group"
                                        invisible="not order_shipped_msg">
                                        <field name="order_shipped_content"
                                            placeholder="e.g. Dear customer, your order has been shipped!" />
                                    </group> -->

                <!-- Order Delivered Message -->
                <!-- <group>
                                        <field name="order_delivered_msg" />
                                    </group>
                                    <group name="order_delivered_group"
                                        invisible="not order_delivered_msg">
                                        <field name="order_delivered_content"
                                            placeholder="e.g. Dear customer, your order has been delivered!" />
                                    </group>


                                </sheet>
                            </div>
                        </div>
                    </div>
                </xpath> -->


            </field>
        </record>

    </data>
</odoo>