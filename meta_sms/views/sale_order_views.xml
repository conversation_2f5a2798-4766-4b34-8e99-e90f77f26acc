<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_sale_order_inherit" model="ir.ui.view">
            <field name="name">sale.order.inherit</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="customer_mobile"/>
                </xpath>

                <xpath expr="//field[@name='payment_term_id']" position="after">
                    <field name="courier_delivery_status" readonly="1"/>
                </xpath>

                <xpath expr="//label[@for='commitment_date']" position="before">
                    <field name="courier_delivery_comment" placeholder="e.g. customer not reached!" />
                </xpath>

                <xpath expr="//header" position="inside">
                    <button name="action_c_shipped" id="action_c_shipped" string="Mark as Shipped" confirm="Are you sure you want to do this? This action can't be undone." type="object" invisible="courier_delivery_status in ['delivered', 'return', 'shipped']"/>
                    <button name="action_c_delivered" id="action_c_delivered" string="Mark as Delivered" confirm="Are you sure you want to do this? This action can't be undone." type="object" invisible="courier_delivery_status in ['pending', 'delivered', 'return']"/>
                    <button name="action_c_return" id="action_c_return" string="Mark as Return" confirm="Are you sure you want to do this? This action can't be undone." type="object" invisible="courier_delivery_status in ['pending', 'return', 'shipped']"/>
                    <button name="action_order_confirmation_msg" id="order_confirmation_msg" string="SEND BY SMS" type="object" invisible="state in ['draft', 'done', 'cancel']"/>
                </xpath>
            </field>
        </record>

        <!-- <record id="view_sale_order_tree_inherit" model="ir.ui.view">
            <field name="name">sale.order.tree.expand</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='invoice_status']" position="after">
                    <field name="courier_delivery_status" invisible="1"/>
                </xpath>
            </field>
        </record> -->
    </data>
</odoo>
