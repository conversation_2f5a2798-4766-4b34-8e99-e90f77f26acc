<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="appointment_type_tennis_court_resource_1" model="appointment.resource">
        <field name="name">Court 1</field>
        <field name="appointment_type_ids" eval="[(4, ref('appointment.appointment_type_tennis_court'))]"/>
    </record>
    <record id="appointment_type_tennis_court_resource_2" model="appointment.resource">
        <field name="name">Court 2</field>
        <field name="appointment_type_ids" eval="[(4, ref('appointment.appointment_type_tennis_court'))]"/>
    </record>
    <record id="appointment_type_tennis_court_resource_3" model="appointment.resource">
        <field name="name">Court 3</field>
        <field name="appointment_type_ids" eval="[(4, ref('appointment.appointment_type_tennis_court'))]"/>
    </record>
    <record id="appointment_type_tennis_court_resource_4" model="appointment.resource">
        <field name="name">Court 4</field>
        <field name="appointment_type_ids" eval="[(4, ref('appointment.appointment_type_tennis_court'))]"/>
    </record>
</odoo>
