<?xml version="1.0" encoding="utf-8"?>
<odoo>

<record id="resource_calendar_leaves_action_show_appointment_resources" model="ir.actions.server">
    <field name="name">Appointment Resource Leaves</field>
    <field name="model_id" ref="resource.model_resource_calendar_leaves"/>
    <field name="binding_view_types">form</field>
    <field name="state">code</field>
    <field name="code">
action = env['ir.actions.act_window']._for_xml_id('resource.action_resource_calendar_leave_tree')
resource_ids = env['appointment.resource'].search([]).sudo().resource_id
calendar_ids = resource_ids.calendar_id
# we do not care about company-wide as it cannot be set from the leaves wizard
action['domain'] = ['|',
                    '&amp;', ('resource_id', '=', False),
                        '|', ('calendar_id', 'in', calendar_ids.ids), ('calendar_id', '=', False),
                    ('resource_id', 'in', resource_ids.ids)]
    </field>
</record>


</odoo>
