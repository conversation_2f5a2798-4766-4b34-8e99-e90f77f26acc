<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web_gantt.GanttRenderer">
        <GanttRendererControls t-props="controlsProps" />
        <div t-ref="grid"
            class="o_gantt_renderer o_renderer bg-view"
            t-att-class="{ o_has_row_headers: hasRowHeaders, 'pe-auto': isDragging }"
            t-att-style="getGridStyle()"
            t-on-pointermove="throttledComputeHoverParams"
            t-on-pointerleave="onPointerLeave"
        >
            <t t-call="{{ constructor.headerTemplate }}"/>
            <t t-if="hasRowHeaders">
                <div class="o_gantt_row_headers o_gantt_row_sidebar o_gantt_grid_rows border-end position-sticky start-0 bg-view">
                    <t t-foreach="rowsToRender" t-as="row" t-key="row.id">
                        <t t-call="{{ constructor.rowHeaderTemplate }}" />
                    </t>
                </div>
            </t>
            <div t-ref="cellContainer" class="o_gantt_cells o_gantt_grid_rows o_gantt_grid_columns position-relative">
                <t t-foreach="rowsToRender" t-as="row" t-key="row.id">
                    <t t-call="{{ constructor.rowContentTemplate }}" />
                </t>
                <t t-foreach="pillsToRender" t-as="pill" t-key="pill.id">
                    <t t-set="row" t-value="getRowFromPill(pill)"/>
                    <t t-if="row.isGroup">
                        <t t-call="{{ constructor.groupPillTemplate }}" />
                    </t>
                    <t t-else="" >
                        <t t-call="{{ constructor.pillTemplate }}" />
                    </t>
                </t>
                <t t-if="shouldRenderConnectors()">
                    <t t-foreach="connectorsToRender" t-as="connector" t-key="connector.id">
                        <GanttConnector
                            reactive="connector"
                            onRemoveButtonClick="() => this.onRemoveButtonClick(connector.id)"
                            onLeftButtonClick="() => this.onRescheduleButtonClick('backward', connector.id)"
                            onRightButtonClick="() => this.onRescheduleButtonClick('forward', connector.id)"
                        />
                    </t>
                </t>
                <GanttResizeBadge reactive="resizeBadgeReactive" />
            </div>
            <t t-if="totalRow">
                <t t-call="{{ constructor.totalRowTemplate }}"/>
            </t>
        </div>
    </t>

    <t t-name="web_gantt.GanttRenderer.Header">
        <t t-if="hasRowHeaders">
            <b
                class="o_gantt_title d-flex align-items-center justify-content-center bg-100 position-sticky start-0 p-2 border-end"
                t-esc="model.metaData.string"
            />
        </t>
        <div class="o_gantt_header_groups o_gantt_grid_columns bg-100 position-sticky">
            <t t-foreach="columnsGroups" t-as="column" t-key="column.id">
                <div class="o_gantt_header_title position-sticky d-flex align-items-center ps-2 overflow-hidden bg-100"
                    t-att-style="getGridPosition({column: column.grid.column})"
                >
                    <t t-esc="model.metaData.scale.groupHeaderFormatter(column.start, env)"/>
                </div>
            </t>
        </div>
        <div class="o_gantt_header_columns o_gantt_grid_columns bg-view position-sticky">
            <t t-foreach="columns" t-as="column" t-key="column.id">
                <div class="o_gantt_header_cell d-flex align-items-center justify-content-center py-2"
                    t-att-class="{ o_gantt_today: column.isToday, 'bg-100': !column.isToday }"
                    t-att-style="getGridPosition({column: column.grid.column})"
                >
                    <t t-if="'colHeaderFormatter' in model.metaData.scale">
                        <t t-esc="model.metaData.scale.colHeaderFormatter(column.start, env)"/>
                    </t>
                    <small t-else="" class="text-center">
                        <t t-if="is24HourFormat">
                            <b t-esc="column.start.toFormat('H')"/>
                        </t>
                        <t t-else="">
                            <b t-esc="column.start.toFormat('h')"/>
                            <div class="d-xl-inline-block" t-esc="column.start.toFormat('a').toLowerCase()"/>
                        </t>
                    </small>
                </div>
            </t>
        </div>
    </t>

    <t t-name="web_gantt.GanttRenderer.ConnectorCreator">
        <div
            class="o_connector_creator_wrapper position-absolute w-100"
            t-attf-class="o_connector_creator_wrapper_{{ alignment.vertical }} {{ '_color' in pill ? 'o_gantt_color_' + pill._color : '' }}"
        >
            <div
                class="o_connector_creator position-absolute"
                t-attf-class="o_connector_creator_{{ alignment.vertical }} o_connector_creator_{{ alignment.horizontal }}"
            >
                <div class="o_connector_creator_bullet position-absolute z-n1 rounded-circle" />
            </div>
        </div>
    </t>

    <t t-name="web_gantt.GanttRenderer.RowHeader">
        <div
            class="o_gantt_row_header o_gantt_row_sidebar position-sticky start-0 bg-view align-items-center"
            t-att-class="{
                o_sample_data_disabled: isDisabled(row),
                o_gantt_row_sidebar_empty: !row.name,
                o_gantt_group: row.isGroup,
                o_gantt_hoverable: isHoverable(row),
                o_mobile_progress_bar: row.progressBar and isTouchDevice,
                o_group_open: !model.isClosed(row.id),
            }"
            t-att-style="getGridPosition({ row: row.grid.row })"
            t-att-data-row-id="row.id"
            t-on-click.synthetic="() => row.isGroup ? this.model.toggleRow(row.id) : this.focusFirstPill(row.id)"
        >
            <div
                class="o_gantt_row_title d-flex align-items-center h-100 w-100 pe-1"
                t-att-class="{ 'fw-bold': row.isGroup }"
                t-att-style="getRowTitleStyle(row)"
                t-att-title="row.name or ''"
            >
                <i t-if="row.isGroup" t-attf-class="o_group_caret fa fa-fw me-1 fa-caret-{{ model.isClosed(row.id) ? 'right' : 'down' }}" />
                <span t-if="row.thumbnailUrl and row.resId"
                    class="o_gantt_row_thumbnail_wrapper"
                    t-att-class="{ 'me-1' : row.isGroup }"
                >
                    <img t-att-src="row.thumbnailUrl" class="o_gantt_row_thumbnail o_avatar rounded"/>
                </span>
                <span class="text-truncate w-0 flex-grow-1">
                    <t t-esc="row.name" />
                </span>
            </div>
            <t t-if="row.progressBar">
                <GanttRowProgressBar t-props="getProgressBarProps(row)" />
            </t>
        </div>
    </t>

    <t t-name="web_gantt.GanttRenderer.RowContent">
        <!-- Cells -->
        <t t-foreach="columns" t-as="column" t-key="column.id">
            <t t-set="col" t-value="column.grid.column[0]" />
            <div
                class="o_gantt_cell"
                t-att-class="ganttCellAttClass(row, column)"
                t-attf-style="{{ getGridPosition({ column: column.grid.column, row: row.grid.row }) }};{{ row.cellColors[column.id] || '' }}"
                t-att-data-row-id="row.id"
                t-att-data-col="col"
                t-on-click.synthetic="(ev) => row.isGroup ? this.model.toggleRow(row.id) : this.onCellClicked(row.id, col)"
            />
        </t>
    </t>

    <t t-name="web_gantt.GanttRenderer.Pill">
        <t t-set="renderConnectors" t-value="shouldRenderRecordConnectors(pill.record)" />
        <div class="o_gantt_pill_wrapper"
            t-att-class="{
                o_sample_data_disabled: isDisabled(row),
                o_draggable: !pill.disableDrag,
                o_undraggable: pill.disableDrag,
                o_resizable: !pill.disableStartResize or !pill.disableStopResize,
                'position-relative': renderConnectors
            }"
            t-att-style="getGridPosition(pill.grid)"
            t-att-data-pill-id="pill.id"
        >
            <t t-if="renderConnectors" t-call="{{ constructor.connectorCreatorTemplate }}">
                <t t-set="alignment" t-value="getConnectorCreatorAlignment('top')" />
            </t>
            <div class="o_gantt_pill position-relative h-100 d-flex align-items-center"
                t-att-class="pill.className"
                t-on-click.synthetic="(ev) => this.onPillClicked(ev, pill)"
            >
                <span
                    t-if="pill._progress"
                    class="position-absolute h-100 o_gantt_progress"
                    t-attf-style="width:{{ pill._progress }}%;"
                />
                <span class="o_gantt_pill_title text-truncate mx-1" t-esc="pill.displayName" />
                <div t-if="pill.disableDrag" class="o_gantt_lock fa fa-lock ms-auto me-2" />
            </div>
            <t t-if="renderConnectors" t-call="{{ constructor.connectorCreatorTemplate }}">
                <t t-set="alignment" t-value="getConnectorCreatorAlignment('bottom')" />
            </t>
        </div>
    </t>

    <t t-name="web_gantt.GanttRenderer.GroupPill">
        <div
            class="o_gantt_pill_wrapper o_gantt_group_pill align-items-center"
            t-att-class="{
                o_sample_data_disabled: isDisabled(row),
                o_group_open: !model.isClosed(row.id),
            }"
            t-att-style="getGridPosition(pill.grid)"
        >
            <div class="o_gantt_pill o_gantt_consolidated_pill position-relative overflow-visible"
                t-att-class="pill.className"
                t-att-title="pill.displayName"
            >
                <span
                    t-if="pill._progress"
                    class="position-absolute h-100 o_gantt_progress"
                    t-attf-style="width:{{ pill._progress }}%;"
                />
            </div>
            <span class="o_gantt_pill_title bg-view text-truncate px-1 z-1" t-esc="pill.displayName" />
        </div>
    </t>

    <t t-name="web_gantt.GanttRenderer.TotalRow">
        <t t-if="hasRowHeaders">
            <div class="o_gantt_row_total o_gantt_row_sidebar border-end position-sticky start-0 bg-view d-flex justify-content-end">
                <h4
                    class="o_gantt_row_title d-flex align-items-center pe-3 my-0 fw-bold"
                    t-att-class="{ o_sample_data_disabled: isDisabled() }"
                    t-att-title="totalRow.name"
                    t-esc="totalRow.name"
                />
            </div>
        </t>
        <div class="o_gantt_row_total o_gantt_grid_columns bg-view">
            <t t-foreach="columns" t-as="column" t-key="column.id">
                <div
                    class="o_gantt_cell"
                    t-att-class="{ o_gantt_today: column.isToday, o_sample_data_disabled: isDisabled() }"
                    t-att-style="getGridPosition({ column: column.grid.column, row: [1, 2] })"
                />
            </t>
            <t t-foreach="totalRow.pills" t-as="pill" t-key="pill.id">
                <div
                    class="o_gantt_pill_wrapper position-relative p-0 h-100 d-flex align-items-end"
                    t-att-class="{ o_sample_data_disabled: isDisabled() }"
                    t-att-style="getGridPosition({ column: pill.grid.column, row: [1, 2] })"
                >
                    <div class="o_gantt_pill o_gantt_consolidated_pill w-100 d-flex align-items-end justify-content-center"
                        t-att-title="pill.displayName"
                        t-attf-style="height: {{ totalRow.factor * pill.aggregateValue }}%;"
                    >
                        <span
                            class="o_gantt_consolidated_pill_title bg-view text-truncate px-1 mb-1"
                            t-att-class="{ 'o_gantt_consolidated_pill_small': this.isPillSmall(pill) }"
                            t-esc="pill.displayName"
                        />
                    </div>
                </div>
            </t>
        </div>
    </t>

</templates>
