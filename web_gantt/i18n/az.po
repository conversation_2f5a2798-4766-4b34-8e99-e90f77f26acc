# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_gantt
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "%(hour)sh%(minute)s"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "%s cannot be scheduled in the past"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "%sh"
msgstr ""

#. module: web_gantt
#: model:ir.model,name:web_gantt.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Fəaliyyət Pəncərə Görünüşü"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Activate dense mode"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Activate sparse mode"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Apply"
msgstr "Tətbiq edin"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Are you sure to delete this record?"
msgstr "Bu qeydi silməyə əminsiniz?"

#. module: web_gantt
#: model:ir.model,name:web_gantt.model_base
msgid "Base"
msgstr "Baza"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Collapse rows"
msgstr "Sıraların Dağılması"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Create"
msgstr "Yaradın"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Edit"
msgstr "Redaktə et"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Ending date cannot be before the starting date"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Expand rows"
msgstr "Cərgələri uzatmaq"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Focus Today"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "From"
msgstr "Başlama Tarixi"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "From: %(from_date)s to: %(to_date)s"
msgstr ""

#. module: web_gantt
#: model:ir.model.fields.selection,name:web_gantt.selection__ir_actions_act_window_view__view_mode__gantt
#: model:ir.model.fields.selection,name:web_gantt.selection__ir_ui_view__type__gantt
msgid "Gantt"
msgstr "Gant"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Gantt View"
msgstr "Gant Görünüşü"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt child can only be field or template, got %s"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'date_start' attribute"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'date_stop' attribute"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid ""
"Gantt must have a 'dependency_inverted_field' attribute once the "
"'dependency_field' is specified"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "Gantt start date"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "Gantt stop date"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt view can contain only one templates tag"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Impossible to schedule in the past."
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Insufficient fields for Gantt View!"
msgstr "Gant Görüntüsü üçün sahələr kifayət etmir!"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid ""
"Invalid attributes (%(invalid_attributes)s) in gantt view. Attributes must "
"be in (%(valid_attributes)s)"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid default_range '%s' in gantt"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid default_scale '%s' in gantt"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid display_mode '%s' in gantt"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Name"
msgstr "Ad"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.xml:0
msgid "New"
msgstr "Yeni"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Open"
msgstr "Açın"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Plan"
msgstr "Plan"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Reschedule done successfully."
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Start"
msgstr "Başlayın"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Starting date cannot be after the ending date"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Stop"
msgstr "Dayanın"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "The dependencies are not valid, there is a cycle."
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "There are no valid candidates to re-plan"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This month"
msgstr "Bu Ay"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This quarter"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This week"
msgstr "Bu Həftə"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This year"
msgstr "Bu İl"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Today"
msgstr "Bu gün"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Toolbar menu"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Total"
msgstr "Cəmi"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "Undefined %s"
msgstr "Təyin olunmayan %s"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
#: model:ir.model,name:web_gantt.model_ir_ui_view
msgid "View"
msgstr "Baxın"

#. module: web_gantt
#: model:ir.model.fields,field_description:web_gantt.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_gantt.field_ir_ui_view__type
msgid "View Type"
msgstr "Növə baxın"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "You cannot move %(record)s towards %(related_record)s."
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "You cannot reschedule %(main_record)s towards %(other_record)s."
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "hours"
msgstr "Saatlar"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "minutes"
msgstr "Dəqiqələr"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "months"
msgstr "Aylar"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "to"
msgstr "Bitmə Tarixi"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_row_progress_bar.xml:0
msgid "{{ props.progressBar.warning }}"
msgstr ""
