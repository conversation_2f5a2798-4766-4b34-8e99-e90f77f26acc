# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_gantt
# 
# Translators:
# MichaelHadar, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-16 20:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "%(hour)sh%(minute)s"
msgstr "%(hour)sש%(minute)s"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "%s cannot be scheduled in the past"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "%sh"
msgstr "%sש"

#. module: web_gantt
#: model:ir.model,name:web_gantt.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "תצוגת חלון פעולה"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Activate dense mode"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Activate sparse mode"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Apply"
msgstr "החל"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Are you sure to delete this record?"
msgstr "האם אתה בטוח שברצונך למחוק רשומה זו?"

#. module: web_gantt
#: model:ir.model,name:web_gantt.model_base
msgid "Base"
msgstr "בסיס"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Collapse rows"
msgstr "צמצם שורות"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Create"
msgstr "יצירה"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Edit"
msgstr "ערוך"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Ending date cannot be before the starting date"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Expand rows"
msgstr "הרחב שורות"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Focus Today"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "From"
msgstr "מ"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "From: %(from_date)s to: %(to_date)s"
msgstr ""

#. module: web_gantt
#: model:ir.model.fields.selection,name:web_gantt.selection__ir_actions_act_window_view__view_mode__gantt
#: model:ir.model.fields.selection,name:web_gantt.selection__ir_ui_view__type__gantt
msgid "Gantt"
msgstr "גאנט"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Gantt View"
msgstr "תצוגת גאנט"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt child can only be field or template, got %s"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'date_start' attribute"
msgstr "גאנט מחייב הגדרת \"תאריך התחלה\":"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'date_stop' attribute"
msgstr "גאנט מחייב הגדרת \"תאריך סיום\":"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid ""
"Gantt must have a 'dependency_inverted_field' attribute once the "
"'dependency_field' is specified"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "Gantt start date"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "Gantt stop date"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt view can contain only one templates tag"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Impossible to schedule in the past."
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Insufficient fields for Gantt View!"
msgstr "אין מספיק שדות לתצוגת גאנט!"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid ""
"Invalid attributes (%(invalid_attributes)s) in gantt view. Attributes must "
"be in (%(valid_attributes)s)"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid default_range '%s' in gantt"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid default_scale '%s' in gantt"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid display_mode '%s' in gantt"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Name"
msgstr "שם"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.xml:0
msgid "New"
msgstr "חדש"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Open"
msgstr "פתוח"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Plan"
msgstr "תוכנית"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Reschedule done successfully."
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Start"
msgstr "התחל"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Starting date cannot be after the ending date"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Stop"
msgstr "עצור"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "The dependencies are not valid, there is a cycle."
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "There are no valid candidates to re-plan"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This month"
msgstr "חודש נוכחי"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This quarter"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This week"
msgstr "שבוע נוכחי"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This year"
msgstr "שנה נוכחית"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Today"
msgstr "היום"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Toolbar menu"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Total"
msgstr "סה\"כ"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "Undefined %s"
msgstr "%s לא מוגדר"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
#: model:ir.model,name:web_gantt.model_ir_ui_view
msgid "View"
msgstr "תצוגה"

#. module: web_gantt
#: model:ir.model.fields,field_description:web_gantt.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_gantt.field_ir_ui_view__type
msgid "View Type"
msgstr "סוג תצוגה"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "You cannot move %(record)s towards %(related_record)s."
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "You cannot reschedule %(main_record)s towards %(other_record)s."
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "hours"
msgstr "שעות"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "minutes"
msgstr "דקות"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "months"
msgstr "חודשים"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "to"
msgstr "ל"
