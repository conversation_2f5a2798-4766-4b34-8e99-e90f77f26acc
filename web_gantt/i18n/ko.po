# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_gantt
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-16 20:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "%(hour)sh%(minute)s"
msgstr "%(hour)s시간 %(minute)s분"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "%s cannot be scheduled in the past"
msgstr "%s는 과거로 예약할 수 없습니다."

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "%sh"
msgstr "%sh"

#. module: web_gantt
#: model:ir.model,name:web_gantt.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "작업 윈도우 보기"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Activate dense mode"
msgstr "dense mode 활성화"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Activate sparse mode"
msgstr "sparse mode 활성화"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Apply"
msgstr "적용"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Are you sure to delete this record?"
msgstr "이 레코드를 삭제 하시겠습니까?"

#. module: web_gantt
#: model:ir.model,name:web_gantt.model_base
msgid "Base"
msgstr "기준액"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Collapse rows"
msgstr "행 축소"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Create"
msgstr "작성"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Edit"
msgstr "편집"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Ending date cannot be before the starting date"
msgstr "종료일은 시작일보다 이전일 수 없습니다."

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Expand rows"
msgstr "행 확장"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Focus Today"
msgstr "오늘의 포커스"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "From"
msgstr "시작 시간"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "From: %(from_date)s to: %(to_date)s"
msgstr "시작일: %(from_date)s 종료일: %(to_date)s"

#. module: web_gantt
#: model:ir.model.fields.selection,name:web_gantt.selection__ir_actions_act_window_view__view_mode__gantt
#: model:ir.model.fields.selection,name:web_gantt.selection__ir_ui_view__type__gantt
msgid "Gantt"
msgstr "간트 차트"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Gantt View"
msgstr "간트 화면"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt child can only be field or template, got %s"
msgstr "간트 하위 메뉴는 필드나 템플릿만 선택 가능하며, 다음 내용입니다 %s"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'date_start' attribute"
msgstr "간트에는 'date_start' 속성을 지정해야 합니다"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'date_stop' attribute"
msgstr "간트에는 'date_stop' 속성을 지정해야 합니다"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid ""
"Gantt must have a 'dependency_inverted_field' attribute once the "
"'dependency_field' is specified"
msgstr ""
"'dependency_field'가 지정되는 경우, 간트에서 'dependency_inverted_field' 속성을 지정해야 합니다."

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "Gantt start date"
msgstr "간트 시작일"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "Gantt stop date"
msgstr "간트 종료일"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt view can contain only one templates tag"
msgstr "간트 뷰에는 서식 태그가 하나만 포함될 수 있습니다"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Impossible to schedule in the past."
msgstr "지난 시점으로는 예약할 수 없습니다."

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Insufficient fields for Gantt View!"
msgstr "간트 차트 화면에 대한 필드가 충분하지 않습니다!"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid ""
"Invalid attributes (%(invalid_attributes)s) in gantt view. Attributes must "
"be in (%(valid_attributes)s)"
msgstr ""
"간트 보기에 잘못된 속성 (%(invalid_attributes)s)이 있습니다. 속성은 (%(valid_attributes)s)에 "
"위치해야 합니다."

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid default_range '%s' in gantt"
msgstr "간트에 잘못된 default_range '%s'가 있습니다."

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid default_scale '%s' in gantt"
msgstr "간트에서 default_scale '%s' 가 유효하지 않습니다"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid display_mode '%s' in gantt"
msgstr "간트에 잘못된 display_mode '%s'가 있습니다."

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Name"
msgstr "이름"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.xml:0
msgid "New"
msgstr "신규"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Open"
msgstr "열기"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Plan"
msgstr "계획"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Reschedule done successfully."
msgstr "일정 변경이 성공적으로 완료되었습니다."

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Start"
msgstr "시작"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Starting date cannot be after the ending date"
msgstr "종료일은 시작일보다 이전일 수 없습니다."

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Stop"
msgstr "중지"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "The dependencies are not valid, there is a cycle."
msgstr "종속성이 유효하지 않으며 주기가 있습니다."

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "There are no valid candidates to re-plan"
msgstr "다시 계획할 수 있는 적격 후보자가 없습니다."

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This month"
msgstr "이번 달"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This quarter"
msgstr "이번 분기"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This week"
msgstr "이번 주"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This year"
msgstr "올해"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Today"
msgstr "오늘"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Toolbar menu"
msgstr "도구 모음 메뉴"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Total"
msgstr "총계"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "Undefined %s"
msgstr "정의되지 않은 %s"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
#: model:ir.model,name:web_gantt.model_ir_ui_view
msgid "View"
msgstr "화면"

#. module: web_gantt
#: model:ir.model.fields,field_description:web_gantt.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_gantt.field_ir_ui_view__type
msgid "View Type"
msgstr "화면 유형"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "You cannot move %(record)s towards %(related_record)s."
msgstr "%(record)s를 %(related_record)s로 이동할 수 없습니다."

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "You cannot reschedule %(main_record)s towards %(other_record)s."
msgstr "%(main_record)s을 %(other_record)s으로 일정을 변경할 수 없습니다. "

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "hours"
msgstr "시간"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "minutes"
msgstr "분"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "months"
msgstr "월"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "to"
msgstr "종료"
