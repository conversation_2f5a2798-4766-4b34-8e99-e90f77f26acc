# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_gantt
# 
# Translators:
# <PERSON><PERSON><PERSON> moradi, 2025
# <PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Hanna <PERSON>, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "%(hour)sh%(minute)s"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "%s cannot be scheduled in the past"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "%sh"
msgstr ""

#. module: web_gantt
#: model:ir.model,name:web_gantt.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "مشاهده پنجره اقدام"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Activate dense mode"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Activate sparse mode"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Apply"
msgstr "اعمال"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Are you sure to delete this record?"
msgstr ""

#. module: web_gantt
#: model:ir.model,name:web_gantt.model_base
msgid "Base"
msgstr "پایه"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Collapse rows"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Create"
msgstr "ایجاد"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Edit"
msgstr "ویرایش"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Ending date cannot be before the starting date"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Expand rows"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Focus Today"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "From"
msgstr "از"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "From: %(from_date)s to: %(to_date)s"
msgstr ""

#. module: web_gantt
#: model:ir.model.fields.selection,name:web_gantt.selection__ir_actions_act_window_view__view_mode__gantt
#: model:ir.model.fields.selection,name:web_gantt.selection__ir_ui_view__type__gantt
msgid "Gantt"
msgstr "گانت"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Gantt View"
msgstr "نمای گانت"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt child can only be field or template, got %s"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'date_start' attribute"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt must have a 'date_stop' attribute"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid ""
"Gantt must have a 'dependency_inverted_field' attribute once the "
"'dependency_field' is specified"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "Gantt start date"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.js:0
msgid "Gantt stop date"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Gantt view can contain only one templates tag"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Impossible to schedule in the past."
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Insufficient fields for Gantt View!"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid ""
"Invalid attributes (%(invalid_attributes)s) in gantt view. Attributes must "
"be in (%(valid_attributes)s)"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid default_range '%s' in gantt"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid default_scale '%s' in gantt"
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/ir_ui_view.py:0
msgid "Invalid display_mode '%s' in gantt"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Name"
msgstr "نام"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.xml:0
msgid "New"
msgstr "جدید"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_controller.js:0
msgid "Open"
msgstr "باز"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Plan"
msgstr "طرح"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "Reschedule done successfully."
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Start"
msgstr "شروع"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Starting date cannot be after the ending date"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_popover.xml:0
msgid "Stop"
msgstr "توقف"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "The dependencies are not valid, there is a cycle."
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "There are no valid candidates to re-plan"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This month"
msgstr "این ماه"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This quarter"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This week"
msgstr "این هفته"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "This year"
msgstr "امسال"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "Today"
msgstr "امروز"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "Toolbar menu"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
msgid "Total"
msgstr "مجموع"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_model.js:0
msgid "Undefined %s"
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer.js:0
#: model:ir.model,name:web_gantt.model_ir_ui_view
msgid "View"
msgstr "نما"

#. module: web_gantt
#: model:ir.model.fields,field_description:web_gantt.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_gantt.field_ir_ui_view__type
msgid "View Type"
msgstr "نوع نما"

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "You cannot move %(record)s towards %(related_record)s."
msgstr ""

#. module: web_gantt
#. odoo-python
#: code:addons/web_gantt/models/models.py:0
msgid "You cannot reschedule %(main_record)s towards %(other_record)s."
msgstr ""

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "hours"
msgstr "ساعت"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "minutes"
msgstr "دقیقه‌"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_arch_parser.js:0
msgid "months"
msgstr "ماه"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_renderer_controls.xml:0
msgid "to"
msgstr "به"

#. module: web_gantt
#. odoo-javascript
#: code:addons/web_gantt/static/src/gantt_row_progress_bar.xml:0
msgid "{{ props.progressBar.warning }}"
msgstr ""
