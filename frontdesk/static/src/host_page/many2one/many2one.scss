.o_frontdesk {
    .o-autocomplete--input {

        // Reproduces the style of `form-control` class.
        // TO DO: add the ability to toggle the `form-control` class in this input.
        display: block;
        width: 100%;
        padding: $input-padding-y $input-padding-x;
        font-family: $input-font-family;
        @include font-size($input-font-size);
        font-weight: $input-font-weight;
        line-height: $input-line-height;
        color: $input-color;
        background-color: $input-bg;
        background-clip: padding-box;
        border: $input-border-width solid $input-border-color;
        appearance: none;
        @include border-radius($input-border-radius, 0);
        @include box-shadow($input-box-shadow);
        @include transition($input-transition);

        &:focus {
            color: $input-focus-color;
            background-color: $input-focus-bg;
            border-color: $input-focus-border-color;
            outline: 0;
            @if $enable-shadows {
                @include box-shadow($input-box-shadow, $input-focus-box-shadow);
            } @else {
                box-shadow: $input-focus-box-shadow;
            }
        }
        // END - `form-control` class.
    }

    .o-autocomplete--dropdown-menu {
        position: absolute !important;
        min-width: 100%;

        .dropdown-item {
            padding: map-get($spacers, 2);
            font-size: $input-font-size;
        }
    }

    .ui-state-active {
        background: $primary;
        color: color-contrast($primary);
    }

    &.o_dark {
        .o-autocomplete--input {
            border: 0;
        }

        .o-autocomplete--input, .dropdown-menu {
            background: $gray-800;
        }

        .o-autocomplete--input, .o-autocomplete--dropdown-menu, .dropdown-item:not(.ui-state-active) {
            color: #fff;
        }
    }
}
