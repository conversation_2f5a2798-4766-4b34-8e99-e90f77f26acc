<?xml version="1.0" encoding="UTF-8"?>

<templates xml:space="preserve">
    <t t-name="frontdesk.RegisterPage">
        <div class="text-center">
            <h1 class="display-6 mb-0">You have been registered!</h1>
            <div class="mt-2 fs-5 text-break opacity-50">
                <t t-if="props.plannedVisitorData?.plannedVisitorMessage" t-out="markupValue"/>
                <t t-else="">
                    Please have a seat.
                    <t t-if="props.hostData">
                        <t t-out="props.hostData.hostName"/>
                    </t>
                    <t t-elif="props.plannedVisitorData?.plannedVisitorHosts?.length">
                        <t t-foreach="props.plannedVisitorData.plannedVisitorHosts" t-as="host" t-key="host.id">
                            <t t-out="host.name"/>,
                        </t>
                    </t>
                    <t t-if="props.hostData or props.plannedVisitorData?.plannedVisitorHosts?.length"> will get back to you.</t>
                </t>
            </div>
        </div>
        <div t-if="props.isDrinkVisible" t-att-class="props.isDrinkVisible ? 'd-flex flex-lg-fill align-items-center' : ''">
            <div class="w-100 text-center">
                <h3 class="mb-4">Do you want something to drink?</h3>
                <div class="col-lg-6 d-grid gap-3 mx-auto">
                    <button class="btn btn-primary" t-on-click="() => props.showScreen('DrinkPage')">Yes, please</button>
                    <button class="btn btn-secondary" t-on-click="() => props.showScreen('EndPage')">No, thank you</button>
                </div>
            </div>
        </div>
        <div t-if="!props.isMobile" class="col-lg-6 d-grid mx-lg-auto mt-auto">
            <button class="btn" t-att-class="props.theme === 'light' ? 'bg-200' : 'bg-700'" t-on-click="props.onClose">Close</button>
        </div>
    </t>
</templates>
