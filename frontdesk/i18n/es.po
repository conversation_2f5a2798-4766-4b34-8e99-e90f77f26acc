# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* frontdesk
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%(name)s just checked-in. Requested Drink: %(drink)s."
msgstr "%(name)s acaba de registrar su entrada. Pidió la bebida: %(drink)s."

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%(station)s Check-In: %(visitor)s"
msgstr "Registrar entrada en %(station)s: %(visitor)s"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%(station)s Check-In: %(visitor)s to meet %(host)s"
msgstr ""
"R:egistrar entrada en %(station)s: %(visitor)s que se reunirá con %(host)s"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%s just checked-in."
msgstr "%s acaba de registrar su entrada."

#. module: frontdesk
#: model:ir.actions.report,print_report_name:frontdesk.frontdesk_visitor_print_badge
msgid "'Badge - %s' % (object.name).replace('/', '')"
msgstr "'Credencial - %s' % (object.name).replace('/', '')"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "22 Oct 24 14:20:10"
msgstr "22. Oktober 2024 14:20:10"

#. module: frontdesk
#: model:mail.template,body_html:frontdesk.frontdesk_mail_template
msgid ""
"<div>\n"
"                    <p>Hello <t t-if=\"ctx.get('host_name')\"><t t-out=\"ctx.get('host_name')\"/>, </t><b><t t-out=\"object.name\"/></b> <t t-if=\"object.phone\">(<t t-out=\"object.phone\"/>)</t><t t-if=\"object.company\">, coming from <t t-out=\"object.company\"/></t> is asking to meet you at <t t-out=\"object.station_id.name\"/>. Please let them know you'll be there shortly.\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                    <p>Hola <t t-if=\"ctx.get('host_name')\"><t t-out=\"ctx.get('host_name')\"/>, </t><b><t t-out=\"object.name\"/></b> <t t-if=\"object.phone\">(<t t-out=\"object.phone\"/>)</t><t t-if=\"object.company\">, que viene de <t t-out=\"object.company\"/></t> solicitó reunirse con usted en <t t-out=\"object.station_id.name\"/>. Hágale saber que estará ahí en un momento.\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: frontdesk
#: model_terms:web_tour.tour,rainbow_man_message:frontdesk.frontdesk_tour
msgid ""
"<span><b>Congratulations!!!</b> You have created your first visitor.\n"
"        </span>"
msgstr ""
"<span><b>¡Enhorabuena!</b> Ha creado su primer visitante.\n"
"        </span>"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "<strong>Visiting:</strong>"
msgstr "<strong>Visitantes:</strong>"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__active
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__active
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__active
msgid "Active"
msgstr "Activo"

#. module: frontdesk
#: model:ir.actions.client,name:frontdesk.frontdesk_visitor_action_configure_properties_field
msgid "Add Properties"
msgstr "Añadir propiedades"

#. module: frontdesk
#: model:res.groups,name:frontdesk.frontdesk_group_administrator
msgid "Administrator"
msgstr "Administrador"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Allow visitor to select a drink during registration"
msgstr ""
"Permita que los visitantes elijan una bebida al momento de registrarse"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Allows the visitor to pick the host of the meeting from the list"
msgstr "Permite al visitante elegir un anfitrión de la lista para su reunión"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Archived"
msgstr "Archivado"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/quick_check_in/quick_check_in.xml:0
msgid "Are you one of these people?"
msgstr "¿Es una de estas personas?"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__authenticate_guest
msgid "Authenticate Guest"
msgstr "Autenticar invitado"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
msgid "Back"
msgstr "Volver"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "By Responsible"
msgstr "Por responsable"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__canceled
msgid "Cancelled"
msgstr "Cancelado"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__check_in
msgid "Check In"
msgstr "Entrada"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__check_out
msgid "Check Out"
msgstr "Salida"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/welcome_page/welcome_page.xml:0
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Check in"
msgstr "Registrar entrada"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Check out"
msgstr "Registrar salida"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "CheckIn"
msgstr "Entrada"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "CheckIn Station"
msgstr "Estación para registrar entrada"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__checked_in
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Checked-In"
msgstr "Entrada registrada"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__checked_out
msgid "Checked-Out"
msgstr "Salida registrada"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Checked-out"
msgstr "Salida registrada"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Checkout"
msgstr "Salida"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Close"
msgstr "Cerrar"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__company_id
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__company_id
msgid "Company"
msgstr "Compañía"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Company Logo"
msgstr "Logotipo de la empresa"

#. module: frontdesk
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_config
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Configuration"
msgstr "Configuración"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Configure Drinks"
msgstr "Configurar bebidas "

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/host_page/host_page.xml:0
msgid "Confirm"
msgstr "Confirmar"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__create_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__create_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__create_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__create_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__create_date
msgid "Created on"
msgstr "Creado el"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__theme__dark
msgid "Dark"
msgstr "Oscuro"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Date"
msgstr "Fecha"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__description
msgid "Description"
msgstr "Descripción"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__display_name
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__display_name
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Do you want something to drink?"
msgstr "¿Le gustaría algo de beber?"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__drink_ids
msgid "Drink"
msgstr "Bebida"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__drink_image
msgid "Drink Image"
msgstr "Imagen de la bebida"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
msgid "Drink Name"
msgstr "Nombre de la bebida"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__served
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Drink Served"
msgstr "Bebida servida"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/drink_page/drink_page.xml:0
msgid "Drink image"
msgstr "Imagen de la bebida"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Drink to Serve"
msgstr "Bebida para servir"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_drink
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_drinks_report
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__drink_ids
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_drinks_config
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_report_drinks
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_report_view_graph
msgid "Drinks"
msgstr "Bebidas"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_drink_to_serve_visitor
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__drink_to_serve
msgid "Drinks to Serve"
msgstr "Bebidas para servir"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Drinks to serve"
msgstr "Bebidas para servir"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__duration
msgid "Duration"
msgstr "Duración"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
msgid "E.g. What's your Name"
msgstr "p. ej. ¿cuál es su nombre?"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__ask_email
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__email
msgid "Email"
msgstr "Correo electrónico"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__mail_template_id
msgid "Email Template"
msgstr "Plantilla de correo electrónico"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Enter the visitor's name."
msgstr "Ingrese el nombre del visitante."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "Favorite"
msgstr "Favorito"

#. module: frontdesk
#: model:ir.actions.client,name:frontdesk.frontdesk_action_install_kiosk_pwa
#: model:ir.model,name:frontdesk.model_frontdesk_frontdesk
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_root
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Frontdesk"
msgstr "Recepción"

#. module: frontdesk
#: model:ir.model,name:frontdesk.model_frontdesk_drink
msgid "Frontdesk Drink"
msgstr "Bebida de recepción"

#. module: frontdesk
#: model:mail.template,name:frontdesk.frontdesk_mail_template
msgid "Frontdesk Email Template"
msgstr "Plantilla de correo electrónico para recepción"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__name
msgid "Frontdesk Name"
msgstr "Nombre de recepción"

#. module: frontdesk
#: model:sms.template,name:frontdesk.frontdesk_sms_template
msgid "Frontdesk SMS Template"
msgstr "Plantilla de SMS para recepción "

#. module: frontdesk
#: model:ir.model,name:frontdesk.model_frontdesk_visitor
msgid "Frontdesk Visitors"
msgstr "Visitantes de recepción"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_guest_on_site_visitor
msgid "Guest On Site"
msgstr "Invitado en el sitio"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__guest_on_site
msgid "Guests On Site"
msgstr "Invitado en el sitio"

#. module: frontdesk
#: model:sms.template,body:frontdesk.frontdesk_sms_template
msgid ""
"Hello, Your visitor {{ object.name }} {{ '(%s)' % object.phone if "
"object.phone else '' }} {{ '(%s)' % object.company if object.company else ''"
" }} wants to meet you at {{ object.station_id.name }}. Please let them know "
"you'll be there shortly."
msgstr ""
"Hola, su invitado {{ object.name }} {{ '(%s)' % object.phone if object.phone"
" else '' }} {{ '(%s)' % object.company if object.company else '' }} desea "
"reunirse con usted en {{ object.station_id.name }}. Hágale saber que estará "
"ahí en un momento."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Henry"
msgstr "Henry"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Here, you'll see list of all the visitors."
msgstr "Aquí tendrá una lista de todos los visitantes."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Host"
msgstr "Anfitrión "

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__host_ids
msgid "Host Name"
msgstr "Nombre del anfitrión "

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__host_selection
msgid "Host Selection"
msgstr "Selección del anfitrión "

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/drink_page/drink_page.xml:0
msgid "How can we delight you?"
msgstr "¿Qué le podemos ofrecer?"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__id
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__id
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__id
msgid "ID"
msgstr "ID"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__image
msgid "Image"
msgstr "Imagen"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Install"
msgstr "Instalar"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__is_favorite
msgid "Is Favorite"
msgstr "Es favorito"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Karen"
msgstr "Karen"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Kiosk"
msgstr "Quiosco"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__kiosk_url
msgid "Kiosk URL"
msgstr "URL del quiosco"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_frontdesk.py:0
msgid "Last Check-In: %s hours ago"
msgstr "Último registro de entrada: hace %s horas"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_frontdesk.py:0
msgid "Last Check-In: %s minutes ago"
msgstr "Último registro de entrada: hace %s minutos"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__write_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__write_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__write_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__write_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__latest_check_in
msgid "Latest Check In"
msgstr "Registro de entrada más reciente"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Let's add a new visitor."
msgstr "Añadamos un nuevo visitante."

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__theme__light
msgid "Light"
msgstr "Claro"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/host_page/many2one/many2one.js:0
msgid "Loading..."
msgstr "Cargando…"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid ""
"Looking for a better way to manage your visitors? \n"
" It begins right here."
msgstr ""
"¿Necesita una mejor forma de gestionar sus invitados?\n"
"Empiece aquí."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "MY PVT LTD"
msgstr "MY PVT LTD"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__message
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
msgid "Message"
msgstr "Mensaje"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "My Station"
msgstr "Mi estación"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__name
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__name
msgid "Name"
msgstr "Nombre"

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_drink
msgid "No drinks to offer to visitors. Let's add one!"
msgstr "No hay bebidas que ofrecer a los visitantes. ¡Añadamos una!"

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_frontdesk_tree
msgid "No stations found. Let's create one!"
msgstr "No se encontraron estaciones. ¡Creemos una!"

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_drink_to_serve_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_guest_on_site_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_planned_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_station_visitor
msgid "No visitors yet. Let's add one!"
msgstr "Todavía no hay visitantes. ¡Añadamos uno!"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "No, thank you"
msgstr "No, gracias"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_company__none
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_email__none
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_phone__none
msgid "None"
msgstr "Ninguno"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/drink_page/drink_page.xml:0
msgid "Nothing, thanks."
msgstr "Nada, gracias."

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__notify_sms
msgid "Notify by SMS"
msgstr "Notificar por SMS"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__notify_discuss
msgid "Notify by discuss"
msgstr "Notificar por Conversaciones"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__notify_email
msgid "Notify by email"
msgstr "Notificar por correo electrónico"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Notify the host on guest arrival"
msgstr "Notifique al anfitrión cuando llegue el invitado"

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_drink_to_serve_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_guest_on_site_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_planned_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_station_visitor
msgid "Odoo helps you to track all information related to your visitors."
msgstr ""
"Odoo le ayuda a rastrear toda la información relacionada con sus visitantes."

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__drink_offer
msgid "Offer Drinks"
msgstr "Ofrecer bebidas"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "On Site"
msgstr "En el sitio"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Open Desk"
msgstr "Abrir mostrador"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Open Kiosk"
msgstr "Abrir quiosco"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Open host chat window when guest arrives"
msgstr "Abra la ventana de chat del anfitrión cuando llegue el invitado"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_company__optional
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_email__optional
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_phone__optional
msgid "Optional"
msgstr "Opcional"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Options"
msgstr "Opciones"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__ask_company
msgid "Organization"
msgstr "Organización"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__pending
msgid "Pending"
msgstr "Pendiente"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__notify_user_ids
msgid "People to Notify"
msgstr "Personas a notificar"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__ask_phone
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__phone
msgid "Phone"
msgstr "Teléfono"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_planned_visitor
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__planned
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Planned"
msgstr "Planificado"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Please have a seat."
msgstr "Por favor, tome asiento."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_qr_expired
msgid "Please rescan it."
msgstr "Por favor, vuelva a escanearlo."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Print Badge"
msgstr "Imprimir credencial"

#. module: frontdesk
#: model:ir.actions.report,name:frontdesk.frontdesk_visitor_print_badge
msgid "Print Visitor Badge"
msgstr "Imprimir credencial de visitante"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__visitor_properties
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Properties"
msgstr "Propiedades"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/welcome_page/welcome_page.xml:0
msgid "QR Code"
msgstr "Código QR"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_qr_expired
msgid "QR Code Expired."
msgstr "Código QR vencido."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/quick_check_in/quick_check_in.xml:0
msgid "Quick Check In"
msgstr "Registro rápido de entrada"

#. module: frontdesk
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_report
msgid "Reporting"
msgstr "Informes"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Request additional information upon registering"
msgstr "Solicite información adicional al momento del registro"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_company__required
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_email__required
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_phone__required
msgid "Required"
msgstr "Requerido"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "Responsible"
msgstr "Responsable"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__responsible_ids
msgid "Responsibles"
msgstr "Responsables"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__sms_template_id
msgid "SMS Template"
msgstr "Plantilla de SMS"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Save the visitor."
msgstr "Guarde el visitante."

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__access_token
msgid "Security Token"
msgstr "Token de seguridad"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Select or create a station on the fly from where the visitor arrived."
msgstr ""
"Seleccione o cree una estación al momento desde el lugar de donde llegó el "
"visitante."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Select the color of the Desk"
msgstr "Seleccione el color del mostrador"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__self_check_in
msgid "Self Check-In"
msgstr "Registrar entrada por cuenta propia"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Send an SMS to the host on guest arrival"
msgstr "Envíe un SMS al anfitrión cuando llegue el invitado"

#. module: frontdesk
#: model:mail.template,description:frontdesk.frontdesk_mail_template
msgid "Sent to hosts on guest arrival"
msgstr "Enviado al anfitrión cuando llegue el invitado"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__sequence
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
msgid "Sequence"
msgstr "Secuencia"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Show a QR code on the welcome screen to check-in from mobile"
msgstr ""
"Muestre un código QR en la pantalla de bienvenida para registrar la entrada "
"desde un móvil"

#. module: frontdesk
#: model:ir.model.fields,help:frontdesk.field_frontdesk_frontdesk__self_check_in
msgid ""
"Shows a QR code in the interface, for guests to check in from their mobile "
"phone."
msgstr ""
"Muestra un código QR en la interfaz para que los invitados registren su "
"entrada desde sus móviles."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Side Message"
msgstr "Mensaje adicional"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__station_id
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Station"
msgstr "Estación"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "Station Name"
msgstr "Nombre de la estación"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_station_visitor
msgid "Station Visitors"
msgstr "Visitantes de la estación "

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_frontdesk_tree
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_stations
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_stations_config
msgid "Stations"
msgstr "Estaciones"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_station_report
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Statistics"
msgstr "Estadísticas"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__state
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Status"
msgstr "Estado"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
msgid "Thank you for registering!"
msgstr "¡Gracias por registrarse! "

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__theme
msgid "Theme"
msgstr "Tema"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Today"
msgstr "Hoy"

#. module: frontdesk
#: model:res.groups,name:frontdesk.frontdesk_group_user
msgid "User"
msgstr "Usuario"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Visitor"
msgstr "Visitante"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__company
msgid "Visitor Company"
msgstr "Empresa del visitante"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__visitor_properties_definition
msgid "Visitor Properties"
msgstr "Propiedades del visitante"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_frontdesk.py:0
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_visitor
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_visitors_report
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__visitor_ids
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_report_visitors
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_visitors
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_station_report_view_graph
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_report_view_graph
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_graph
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_pivot
msgid "Visitors"
msgstr "Visitantes"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/welcome_page/welcome_page.xml:0
msgid "Welcome to"
msgstr "Bienvenido/a a"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
msgid "Who are you visiting?"
msgstr "¿A quién visita?"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
msgid "Who are you?"
msgstr "¿Quién es usted?"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Write message..."
msgstr "Escriba un mensaje..."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Yes, please"
msgstr "Sí, por favor"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "You have been registered!"
msgstr "¡Se ha registrado!"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Company"
msgstr "Su empresa "

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Email"
msgstr "Su correo electrónico"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Name"
msgstr "Su nombre"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Phone Number"
msgstr "Su número de teléfono"

#. module: frontdesk
#: model:mail.template,subject:frontdesk.frontdesk_mail_template
msgid "Your Visitor {{ object.name }} Requested To Meet You"
msgstr "Su visitante {{ object.name }} solicitó reunirse con usted"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
msgid "Your drink is on the way."
msgstr "Su bebida ya está en camino."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
msgid "e.g. Coca-Cola"
msgstr "p. ej. Coca-Cola"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "e.g. John Doe"
msgstr "p. ej. John Doe"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "e.g. My Company"
msgstr "p. ej. Mi Compañía"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "e.g. <EMAIL>"
msgstr "p. ej. <EMAIL>"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
msgid "has been informed."
msgstr "ha sido informado."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "will get back to you."
msgstr "se pondrá en contacto con usted."
