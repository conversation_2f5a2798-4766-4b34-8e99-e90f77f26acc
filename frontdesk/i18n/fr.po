# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* frontdesk
# 
# Translators:
# <PERSON>on <PERSON>do<PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%(name)s just checked-in. Requested Drink: %(drink)s."
msgstr "%(name)s vient d'arriver. Boissons demandée : %(drink)s."

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%(station)s Check-In: %(visitor)s"
msgstr "%(station)s Check-In : %(visitor)s"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%(station)s Check-In: %(visitor)s to meet %(host)s"
msgstr "%(station)s Check-In : %(visitor)s pour rencontrer %(host)s"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%s just checked-in."
msgstr "%s vient d'arriver."

#. module: frontdesk
#: model:ir.actions.report,print_report_name:frontdesk.frontdesk_visitor_print_badge
msgid "'Badge - %s' % (object.name).replace('/', '')"
msgstr "'Badge - %s' % (object.name).replace('/', '')"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "22 Oct 24 14:20:10"
msgstr "22 Oct 24 14:20:10"

#. module: frontdesk
#: model:mail.template,body_html:frontdesk.frontdesk_mail_template
msgid ""
"<div>\n"
"                    <p>Hello <t t-if=\"ctx.get('host_name')\"><t t-out=\"ctx.get('host_name')\"/>, </t><b><t t-out=\"object.name\"/></b> <t t-if=\"object.phone\">(<t t-out=\"object.phone\"/>)</t><t t-if=\"object.company\">, coming from <t t-out=\"object.company\"/></t> is asking to meet you at <t t-out=\"object.station_id.name\"/>. Please let them know you'll be there shortly.\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                    <p>Bonjour <t t-if=\"ctx.get('host_name')\"><t t-out=\"ctx.get('host_name')\"/>, </t><b><t t-out=\"object.name\"/></b> <t t-if=\"object.phone\">(<t t-out=\"object.phone\"/>)</t><t t-if=\"object.company\">, de <t t-out=\"object.company\"/></t> demande à vous rencontrer à <t t-out=\"object.station_id.name\"/>. Veuillez l'informer que vous serez là sous peu.\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: frontdesk
#: model_terms:web_tour.tour,rainbow_man_message:frontdesk.frontdesk_tour
msgid ""
"<span><b>Congratulations!!!</b> You have created your first visitor.\n"
"        </span>"
msgstr ""
"<span><b>Félicitations !!!</b> Vous avez créé votre premier visiteur.\n"
"        </span>"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "<strong>Visiting:</strong>"
msgstr "<strong>Visite :</strong>"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__active
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__active
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__active
msgid "Active"
msgstr "Actif"

#. module: frontdesk
#: model:ir.actions.client,name:frontdesk.frontdesk_visitor_action_configure_properties_field
msgid "Add Properties"
msgstr "Ajouter des propriétés"

#. module: frontdesk
#: model:res.groups,name:frontdesk.frontdesk_group_administrator
msgid "Administrator"
msgstr "Administrateur"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Allow visitor to select a drink during registration"
msgstr ""
"Permettre au visiteur de sélectionner une boissons pendant l'enregistrement"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Allows the visitor to pick the host of the meeting from the list"
msgstr "Permet au visiteur de choisir l'hôte de la réunion dans la liste"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Archived"
msgstr "Archivé"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/quick_check_in/quick_check_in.xml:0
msgid "Are you one of these people?"
msgstr "Êtes-vous l'une de ces personnes ?"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__authenticate_guest
msgid "Authenticate Guest"
msgstr "Authentifier l'invité"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
msgid "Back"
msgstr "Retour"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "By Responsible"
msgstr "Par responsable"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
msgid "Cancel"
msgstr "Annuler"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__canceled
msgid "Cancelled"
msgstr "Annulé"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__check_in
msgid "Check In"
msgstr "Arrivée"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__check_out
msgid "Check Out"
msgstr "Départ"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/welcome_page/welcome_page.xml:0
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Check in"
msgstr "Arrivée"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Check out"
msgstr "Départ"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "CheckIn"
msgstr "Arrivée"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "CheckIn Station"
msgstr "Guichet d'enregistrement"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__checked_in
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Checked-In"
msgstr "Enregistré"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__checked_out
msgid "Checked-Out"
msgstr "Parti"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Checked-out"
msgstr "Parti"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Checkout"
msgstr "Paiement"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Close"
msgstr "Fermer"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__company_id
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__company_id
msgid "Company"
msgstr "Société"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Company Logo"
msgstr "Logo de la société"

#. module: frontdesk
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_config
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Configuration"
msgstr "Configuration"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Configure Drinks"
msgstr "Configurer les boissons"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/host_page/host_page.xml:0
msgid "Confirm"
msgstr "Confirmer"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__create_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__create_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__create_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__create_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__create_date
msgid "Created on"
msgstr "Créé le"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__theme__dark
msgid "Dark"
msgstr "Sombre"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Date"
msgstr "Date"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__description
msgid "Description"
msgstr "Description"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__display_name
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__display_name
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Do you want something to drink?"
msgstr "Voulez-vous boire quelque chose ?"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__drink_ids
msgid "Drink"
msgstr "Boisson"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__drink_image
msgid "Drink Image"
msgstr "Image de la boisson"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
msgid "Drink Name"
msgstr "Nom de la boisson"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__served
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Drink Served"
msgstr "Boisson servie"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/drink_page/drink_page.xml:0
msgid "Drink image"
msgstr "Image de la boisson"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Drink to Serve"
msgstr "Boisson à servir"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_drink
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_drinks_report
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__drink_ids
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_drinks_config
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_report_drinks
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_report_view_graph
msgid "Drinks"
msgstr "Boissons"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_drink_to_serve_visitor
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__drink_to_serve
msgid "Drinks to Serve"
msgstr "Boissons à servir"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Drinks to serve"
msgstr "Boissons à servir"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__duration
msgid "Duration"
msgstr "Durée"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
msgid "E.g. What's your Name"
msgstr "Par ex. Quel est votre nom ?"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__ask_email
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__email
msgid "Email"
msgstr "E-mail"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__mail_template_id
msgid "Email Template"
msgstr "Modèle d'e-mail"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Enter the visitor's name."
msgstr "Saisissez le nom du visiteur."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "Favorite"
msgstr "Favori"

#. module: frontdesk
#: model:ir.actions.client,name:frontdesk.frontdesk_action_install_kiosk_pwa
#: model:ir.model,name:frontdesk.model_frontdesk_frontdesk
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_root
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Frontdesk"
msgstr "Réception"

#. module: frontdesk
#: model:ir.model,name:frontdesk.model_frontdesk_drink
msgid "Frontdesk Drink"
msgstr "Boisson réception"

#. module: frontdesk
#: model:mail.template,name:frontdesk.frontdesk_mail_template
msgid "Frontdesk Email Template"
msgstr "Modèle d'e-mail réception"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__name
msgid "Frontdesk Name"
msgstr "Nom de la réception"

#. module: frontdesk
#: model:sms.template,name:frontdesk.frontdesk_sms_template
msgid "Frontdesk SMS Template"
msgstr "Modèle de SMS réception"

#. module: frontdesk
#: model:ir.model,name:frontdesk.model_frontdesk_visitor
msgid "Frontdesk Visitors"
msgstr "Visiteurs réception"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Group By"
msgstr "Regrouper par"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_guest_on_site_visitor
msgid "Guest On Site"
msgstr "Invité présent"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__guest_on_site
msgid "Guests On Site"
msgstr "Invités présents"

#. module: frontdesk
#: model:sms.template,body:frontdesk.frontdesk_sms_template
msgid ""
"Hello, Your visitor {{ object.name }} {{ '(%s)' % object.phone if "
"object.phone else '' }} {{ '(%s)' % object.company if object.company else ''"
" }} wants to meet you at {{ object.station_id.name }}. Please let them know "
"you'll be there shortly."
msgstr ""
"Bonjour, Votre visiteur {{ object.name }} {{ '(%s)' % object.phone if "
"object.phone else '' }} {{ '(%s)' % object.company if object.company else ''"
" }} souhaite vous rencontrer à {{ object.station_id.name }}. Veuillez "
"l'informer que vous serez là sous peu."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Henry"
msgstr "Henry"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Here, you'll see list of all the visitors."
msgstr "Vous verrez ici la liste de tous les visiteurs."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Host"
msgstr "Hôte"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__host_ids
msgid "Host Name"
msgstr "Nom de l'hôte"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__host_selection
msgid "Host Selection"
msgstr "Sélection de l'hôte"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/drink_page/drink_page.xml:0
msgid "How can we delight you?"
msgstr "Qu'est-ce qui vous ferait plaisir ? "

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__id
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__id
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__id
msgid "ID"
msgstr "ID"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__image
msgid "Image"
msgstr "Image"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Install"
msgstr "Installer"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__is_favorite
msgid "Is Favorite"
msgstr "Est favori"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Karen"
msgstr "Karen"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Kiosk"
msgstr "Kiosque"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__kiosk_url
msgid "Kiosk URL"
msgstr "URL kiosque"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_frontdesk.py:0
msgid "Last Check-In: %s hours ago"
msgstr "Dernier enregistrement : il y a %s heures"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_frontdesk.py:0
msgid "Last Check-In: %s minutes ago"
msgstr "Dernier enregistrement : il y a %s minutes"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__write_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__write_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__write_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__write_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__latest_check_in
msgid "Latest Check In"
msgstr "Dernier enregistrement"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Let's add a new visitor."
msgstr "Ajoutons un nouveau visiteur."

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__theme__light
msgid "Light"
msgstr "Clair"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/host_page/many2one/many2one.js:0
msgid "Loading..."
msgstr "En cours de chargement..."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid ""
"Looking for a better way to manage your visitors? \n"
" It begins right here."
msgstr ""
"Vous recherchez une meilleure façon de gérer vos visiteurs ? \n"
"Commencez ici."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "MY PVT LTD"
msgstr "MY PVT LTD"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__message
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
msgid "Message"
msgstr "Message"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "My Station"
msgstr "Mon guichet"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__name
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__name
msgid "Name"
msgstr "Nom"

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_drink
msgid "No drinks to offer to visitors. Let's add one!"
msgstr "Rien à offrir à vos visiteurs. Ajoutons une boisson !"

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_frontdesk_tree
msgid "No stations found. Let's create one!"
msgstr "Aucune station trouvée. Créons-en une !"

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_drink_to_serve_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_guest_on_site_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_planned_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_station_visitor
msgid "No visitors yet. Let's add one!"
msgstr "Pas encore de visiteurs. Ajoutons-en un !"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "No, thank you"
msgstr "Non, merci"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_company__none
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_email__none
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_phone__none
msgid "None"
msgstr "Aucun"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/drink_page/drink_page.xml:0
msgid "Nothing, thanks."
msgstr "Rien, merci."

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__notify_sms
msgid "Notify by SMS"
msgstr "Notifier par SMS"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__notify_discuss
msgid "Notify by discuss"
msgstr "Notifier par Discussion"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__notify_email
msgid "Notify by email"
msgstr "Notifier par e-mail"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Notify the host on guest arrival"
msgstr "Notifier l'hôte de l'arrivée de l'invité"

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_drink_to_serve_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_guest_on_site_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_planned_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_station_visitor
msgid "Odoo helps you to track all information related to your visitors."
msgstr ""
"Odoo vous aide à suivre toutes les informations liées à vos visiteurs."

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__drink_offer
msgid "Offer Drinks"
msgstr "Proposer des boissons"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "On Site"
msgstr "Sur site"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Open Desk"
msgstr "Ouvrir le guichet"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Open Kiosk"
msgstr "Ouvrir le kiosque"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Open host chat window when guest arrives"
msgstr "Ouvrir la fenêtre de discussion de l'hôte à l'arrivée de l'invité"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_company__optional
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_email__optional
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_phone__optional
msgid "Optional"
msgstr "Facultatif"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Options"
msgstr "Options"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__ask_company
msgid "Organization"
msgstr "Organisation"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__pending
msgid "Pending"
msgstr "En attente"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__notify_user_ids
msgid "People to Notify"
msgstr "Personnes à notifier"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__ask_phone
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__phone
msgid "Phone"
msgstr "Téléphone"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_planned_visitor
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__planned
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Planned"
msgstr "Planifié"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Please have a seat."
msgstr "Veuillez vous asseoir."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_qr_expired
msgid "Please rescan it."
msgstr "Veuillez le rescanner."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Print Badge"
msgstr "Imprimer le badge"

#. module: frontdesk
#: model:ir.actions.report,name:frontdesk.frontdesk_visitor_print_badge
msgid "Print Visitor Badge"
msgstr "Imprimer le badge du visiteur"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__visitor_properties
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Properties"
msgstr "Propriétés"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/welcome_page/welcome_page.xml:0
msgid "QR Code"
msgstr "Code QR"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_qr_expired
msgid "QR Code Expired."
msgstr "Code QR expiré."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/quick_check_in/quick_check_in.xml:0
msgid "Quick Check In"
msgstr "Enregistrement rapide"

#. module: frontdesk
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_report
msgid "Reporting"
msgstr "Analyse"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Request additional information upon registering"
msgstr "Demander plus d'informations lors de l'enregistrement"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_company__required
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_email__required
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_phone__required
msgid "Required"
msgstr "Requis"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "Responsible"
msgstr "Responsable"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__responsible_ids
msgid "Responsibles"
msgstr "Responsables"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__sms_template_id
msgid "SMS Template"
msgstr "Modèle SMS"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Save the visitor."
msgstr "Enregistrez le visiteur."

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__access_token
msgid "Security Token"
msgstr "Jeton de sécurité"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Select or create a station on the fly from where the visitor arrived."
msgstr ""
"Sélectionnez ou créez une station à partir de laquelle le visiteur est "
"arrivé."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Select the color of the Desk"
msgstr "Sélectionner la couleur du guichet"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__self_check_in
msgid "Self Check-In"
msgstr "Enregistrement autonome"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Send an SMS to the host on guest arrival"
msgstr "Envoyer un SMS à l'hôte à l'arrivée de l'invité"

#. module: frontdesk
#: model:mail.template,description:frontdesk.frontdesk_mail_template
msgid "Sent to hosts on guest arrival"
msgstr "Envoyé aux hôtes à l'arrivée de l'invité"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__sequence
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
msgid "Sequence"
msgstr "Séquence"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Show a QR code on the welcome screen to check-in from mobile"
msgstr ""
"Afficher un code QR sur l'écran de bienvenue pour permettre l'enregistrement"
" à partir d'un téléphone portable"

#. module: frontdesk
#: model:ir.model.fields,help:frontdesk.field_frontdesk_frontdesk__self_check_in
msgid ""
"Shows a QR code in the interface, for guests to check in from their mobile "
"phone."
msgstr ""
"Affiche un code QR sur l'interface, pour que les invités puissent "
"s'enregistrer avec leur téléphone portable."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Side Message"
msgstr "Message supplémentaire"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__station_id
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Station"
msgstr "Guichet"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "Station Name"
msgstr "Nom du guichet"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_station_visitor
msgid "Station Visitors"
msgstr "Visiteurs du guichet"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_frontdesk_tree
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_stations
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_stations_config
msgid "Stations"
msgstr "Guichets"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_station_report
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Statistics"
msgstr "Statistiques"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__state
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Status"
msgstr "Statut"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
msgid "Thank you for registering!"
msgstr "Merci de vous être enregistré !"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__theme
msgid "Theme"
msgstr "Thème"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Today"
msgstr "Aujourd'hui"

#. module: frontdesk
#: model:res.groups,name:frontdesk.frontdesk_group_user
msgid "User"
msgstr "Utilisateur"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Visitor"
msgstr "Visiteur"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__company
msgid "Visitor Company"
msgstr "Société du visiteur"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__visitor_properties_definition
msgid "Visitor Properties"
msgstr "Propriétés du visiteur"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_frontdesk.py:0
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_visitor
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_visitors_report
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__visitor_ids
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_report_visitors
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_visitors
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_station_report_view_graph
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_report_view_graph
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_graph
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_pivot
msgid "Visitors"
msgstr "Visiteurs"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/welcome_page/welcome_page.xml:0
msgid "Welcome to"
msgstr "Bienvenue dans"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
msgid "Who are you visiting?"
msgstr "À qui rendez-vous visite ?"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
msgid "Who are you?"
msgstr "Qui êtes-vous ?"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Write message..."
msgstr "Écrivez un message..."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Yes, please"
msgstr "Oui, avec plaisir"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "You have been registered!"
msgstr "Vous êtes enregistré !"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Company"
msgstr "Votre société"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Email"
msgstr "Votre e-mail"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Name"
msgstr "Votre nom"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Phone Number"
msgstr "Votre numéro de téléphone"

#. module: frontdesk
#: model:mail.template,subject:frontdesk.frontdesk_mail_template
msgid "Your Visitor {{ object.name }} Requested To Meet You"
msgstr "Votre visiteur {{ object.name }} a demandé à vous rencontrer"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
msgid "Your drink is on the way."
msgstr "Votre boisson arrive."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
msgid "e.g. Coca-Cola"
msgstr "par ex. Coca Cola"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "e.g. John Doe"
msgstr "par ex. John Doe"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "e.g. My Company"
msgstr "par ex. My Company"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "e.g. <EMAIL>"
msgstr "par ex. <EMAIL>"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
msgid "has been informed."
msgstr "a été informé(e)."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "will get back to you."
msgstr "vous contactera sous peu."
