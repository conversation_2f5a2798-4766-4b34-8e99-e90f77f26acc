<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="frontdesk" name="Frontdesk">
        <t t-call="web.frontend_layout">
            <t t-set="head">
                <t t-call-assets="frontdesk.assets_frontdesk" lazy_load="True"/>
                <script type="text/javascript">
                    odoo.__session_info__ = <t t-out="json.dumps({
                        'user_context': {'lang': current_lang},
                    })"/>;
                </script>
            </t>
            <t t-set="title" t-value="frontdesk.name"/>
            <t t-set="body_classname" t-value="'o_frontdesk ' + ('bg-900 o_dark' if frontdesk.theme == 'dark' else 'o_light')"/>
            <t t-set="no_header" t-value="1"/>
            <t t-set="no_footer" t-value="1"/>
            <t t-set="no_livechat" t-value="1"/>
            <owl-component name="frontdesk" t-att-props="json.dumps({
                'id': frontdesk.id,
                'isMobile': is_mobile,
                'currentLang': current_lang,
            })"/>
        </t>
    </template>
</odoo>
