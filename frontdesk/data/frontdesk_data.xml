<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Drinks -->
        <record model="frontdesk.drink" id="frontdesk.frontdesk_drink_1">
            <field name="name">Water</field>
            <field name="notify_user_ids" eval="[(4, ref('base.user_admin'))]"/>
            <field name="drink_image" type="base64" file="frontdesk/static/img/water.jpg"/>
        </record>

        <record model="frontdesk.drink" id="frontdesk.frontdesk_drink_2">
            <field name="name">Cola</field>
            <field name="notify_user_ids" eval="[(4, ref('base.user_admin'))]"/>
            <field name="drink_image" type="base64" file="frontdesk/static/img/cola.jpg"/>
        </record>
    </data>
</odoo>
