version: '3.1'
services:
  web:
    image: odoo:18
    depends_on:
      - db
    ports:
      - "8069:8069"
    volumes:
      - odoo-web-data:/var/lib/odoo
      - ./config:/etc/odoo
      - ./addons:/mnt/extra-addons
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo
  db:
    image: postgres:16
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=odoo
      - POSTGRES_PASSWORD=admin
    volumes:
      - odoo-db-data:/var/lib/postgresql/data
volumes:
  odoo-web-data:
  odoo-db-data: